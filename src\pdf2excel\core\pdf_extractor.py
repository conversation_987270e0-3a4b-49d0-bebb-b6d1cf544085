"""PDF content extraction with OCR support."""

import io
import re
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from decimal import Decimal

import PyPDF2
import pdfplumber
import fitz  # PyMuPDF
import pytesseract
from PIL import Image
from pdf2image import convert_from_path
import pandas as pd

from ..models.order_data import ExtractionContext
from ..utils.logger import get_logger
from ..utils.config import Config

logger = get_logger(__name__)


class PDFExtractor:
    """Extract content from PDF files with OCR fallback."""
    
    def __init__(self, config: Config):
        """Initialize PDF extractor with configuration."""
        self.config = config
        self._setup_tesseract()
    
    def _setup_tesseract(self) -> None:
        """Configure Tesseract OCR."""
        if self.config.tesseract_cmd != "tesseract":
            pytesseract.pytesseract.tesseract_cmd = self.config.tesseract_cmd
        logger.info(f"Tesseract configured: {pytesseract.get_tesseract_version()}")
    
    def extract_content(self, pdf_path: str) -> List[ExtractionContext]:
        """
        Extract content from PDF file.
        
        Args:
            pdf_path: Path to PDF file
            
        Returns:
            List of extraction contexts for each page
        """
        pdf_path = Path(pdf_path)
        if not pdf_path.exists():
            raise FileNotFoundError(f"PDF file not found: {pdf_path}")
        
        logger.info(f"Extracting content from: {pdf_path}")
        contexts = []
        
        try:
            # Try text-based extraction first
            contexts = self._extract_text_based(pdf_path)
            
            # Check if OCR is needed
            if self._needs_ocr(contexts):
                logger.info("Text extraction insufficient, using OCR")
                ocr_contexts = self._extract_with_ocr(pdf_path)
                contexts = self._merge_contexts(contexts, ocr_contexts)
                
        except Exception as e:
            logger.error(f"Error extracting PDF content: {e}")
            # Fallback to OCR
            try:
                contexts = self._extract_with_ocr(pdf_path)
            except Exception as ocr_error:
                logger.error(f"OCR extraction also failed: {ocr_error}")
                raise
        
        logger.info(f"Extracted content from {len(contexts)} pages")
        return contexts
    
    def _extract_text_based(self, pdf_path: Path) -> List[ExtractionContext]:
        """Extract text using pdfplumber for better table detection."""
        contexts = []
        
        with pdfplumber.open(pdf_path) as pdf:
            for page_num, page in enumerate(pdf.pages, 1):
                try:
                    # Extract text
                    raw_text = page.extract_text() or ""
                    
                    # Extract tables
                    tables = []
                    page_tables = page.extract_tables()
                    if page_tables:
                        for i, table in enumerate(page_tables):
                            if table:  # Skip empty tables
                                df = pd.DataFrame(table[1:], columns=table[0] if table[0] else None)
                                tables.append({
                                    'table_id': i,
                                    'data': df.to_dict('records'),
                                    'headers': table[0] if table else [],
                                    'raw_table': table
                                })
                    
                    context = ExtractionContext(
                        file_path=str(pdf_path),
                        page_number=page_num,
                        extraction_method="text_based",
                        raw_text=raw_text,
                        tables=tables,
                        metadata={
                            'page_width': page.width,
                            'page_height': page.height,
                            'rotation': getattr(page, 'rotation', 0)
                        }
                    )
                    contexts.append(context)
                    
                except Exception as e:
                    logger.warning(f"Error extracting page {page_num}: {e}")
                    # Create empty context for failed page
                    context = ExtractionContext(
                        file_path=str(pdf_path),
                        page_number=page_num,
                        extraction_method="text_based_failed",
                        raw_text="",
                        tables=[],
                        metadata={'error': str(e)}
                    )
                    contexts.append(context)
        
        return contexts
    
    def _extract_with_ocr(self, pdf_path: Path) -> List[ExtractionContext]:
        """Extract text using OCR."""
        contexts = []
        
        try:
            # Convert PDF to images
            images = convert_from_path(
                pdf_path,
                dpi=self.config.ocr_dpi,
                fmt='PNG'
            )
            
            for page_num, image in enumerate(images, 1):
                try:
                    # Perform OCR
                    ocr_data = pytesseract.image_to_data(
                        image,
                        lang=self.config.ocr_language,
                        output_type=pytesseract.Output.DICT
                    )
                    
                    # Extract text
                    raw_text = pytesseract.image_to_string(
                        image,
                        lang=self.config.ocr_language
                    )
                    
                    # Calculate confidence score
                    confidences = [int(conf) for conf in ocr_data['conf'] if int(conf) > 0]
                    avg_confidence = sum(confidences) / len(confidences) if confidences else 0
                    
                    # Try to detect tables in OCR text
                    tables = self._detect_tables_in_text(raw_text)
                    
                    context = ExtractionContext(
                        file_path=str(pdf_path),
                        page_number=page_num,
                        extraction_method="ocr",
                        confidence_score=avg_confidence,
                        raw_text=raw_text,
                        tables=tables,
                        metadata={
                            'ocr_data': ocr_data,
                            'image_size': image.size,
                            'avg_confidence': avg_confidence
                        }
                    )
                    contexts.append(context)
                    
                except Exception as e:
                    logger.warning(f"OCR failed for page {page_num}: {e}")
                    context = ExtractionContext(
                        file_path=str(pdf_path),
                        page_number=page_num,
                        extraction_method="ocr_failed",
                        raw_text="",
                        tables=[],
                        metadata={'error': str(e)}
                    )
                    contexts.append(context)
                    
        except Exception as e:
            logger.error(f"Failed to convert PDF to images: {e}")
            raise
        
        return contexts
    
    def _needs_ocr(self, contexts: List[ExtractionContext]) -> bool:
        """Determine if OCR is needed based on text extraction quality."""
        if not contexts:
            return True
        
        total_text_length = sum(len(ctx.raw_text.strip()) for ctx in contexts)
        avg_text_per_page = total_text_length / len(contexts)
        
        # If average text per page is very low, likely scanned
        if avg_text_per_page < 50:
            return True
        
        # Check for common indicators of scanned documents
        for context in contexts:
            text = context.raw_text.lower()
            # Look for garbled text or too many special characters
            if len(re.findall(r'[^\w\s\-\.\,\:\;\(\)\[\]\/]', text)) > len(text) * 0.1:
                return True
        
        return False
    
    def _merge_contexts(self, text_contexts: List[ExtractionContext], 
                       ocr_contexts: List[ExtractionContext]) -> List[ExtractionContext]:
        """Merge text-based and OCR contexts, preferring better quality."""
        merged = []
        
        for i in range(max(len(text_contexts), len(ocr_contexts))):
            text_ctx = text_contexts[i] if i < len(text_contexts) else None
            ocr_ctx = ocr_contexts[i] if i < len(ocr_contexts) else None
            
            if text_ctx and ocr_ctx:
                # Choose based on content quality
                if len(text_ctx.raw_text.strip()) > len(ocr_ctx.raw_text.strip()) * 0.8:
                    # Text extraction is decent, use it but add OCR metadata
                    text_ctx.metadata['ocr_confidence'] = ocr_ctx.confidence_score
                    merged.append(text_ctx)
                else:
                    # OCR is better
                    merged.append(ocr_ctx)
            elif text_ctx:
                merged.append(text_ctx)
            elif ocr_ctx:
                merged.append(ocr_ctx)
        
        return merged
    
    def _detect_tables_in_text(self, text: str) -> List[Dict[str, Any]]:
        """Attempt to detect table-like structures in plain text."""
        tables = []
        lines = text.split('\n')
        
        # Look for lines that might be table rows (multiple columns separated by spaces/tabs)
        potential_rows = []
        for line in lines:
            # Skip empty lines
            if not line.strip():
                continue
            
            # Look for lines with multiple "columns" (words separated by significant whitespace)
            columns = re.split(r'\s{2,}|\t+', line.strip())
            if len(columns) >= 3:  # At least 3 columns to consider it a table row
                potential_rows.append(columns)
        
        if len(potential_rows) >= 2:  # At least header + 1 data row
            # Try to identify header row (often the first row with text)
            header_idx = 0
            for i, row in enumerate(potential_rows):
                if any(col.replace(' ', '').isalpha() for col in row):
                    header_idx = i
                    break
            
            headers = potential_rows[header_idx] if header_idx < len(potential_rows) else []
            data_rows = potential_rows[header_idx + 1:] if header_idx + 1 < len(potential_rows) else []
            
            if data_rows:
                tables.append({
                    'table_id': 0,
                    'headers': headers,
                    'data': [dict(zip(headers, row)) for row in data_rows if len(row) == len(headers)],
                    'raw_table': [headers] + data_rows,
                    'extraction_method': 'text_pattern'
                })
        
        return tables
