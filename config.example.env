# PDF2Excel Configuration Example
# Copy this file to .env and customize as needed

# OCR Settings
PDF2EXCEL_TESSERACT_CMD=tesseract
PDF2EXCEL_OCR_LANGUAGE=eng
PDF2EXCEL_OCR_DPI=300

# PDF Processing
PDF2EXCEL_PDF_PASSWORD=
PDF2EXCEL_EXTRACT_IMAGES=true

# Data Validation
PDF2EXCEL_MIN_QUANTITY=0.01
PDF2EXCEL_MAX_QUANTITY=999999.99
PDF2EXCEL_VALID_CURRENCIES=["USD", "EUR", "GBP", "CAD", "AUD"]

# Excel Output
PDF2EXCEL_EXCEL_SHEET_NAME=SAP_Import
PDF2EXCEL_DATE_FORMAT=YYYY.MM.DD
PDF2EXCEL_DECIMAL_PLACES=2

# Logging
PDF2EXCEL_LOG_LEVEL=INFO
PDF2EXCEL_LOG_FILE=pdf2excel.log

# Custom Field Patterns (JSON format)
# Uncomment and customize for your specific PDF formats
# PDF2EXCEL_FIELD_PATTERNS={
#   "order_number": [
#     "order\\s*(?:number|no|#)[\\s:]*([A-Z0-9\\-]+)",
#     "po\\s*(?:number|no|#)[\\s:]*([A-Z0-9\\-]+)"
#   ],
#   "customer_code": [
#     "customer\\s*(?:code|id|number)[\\s:]*([A-Z0-9\\-]+)",
#     "account\\s*(?:code|id|number)[\\s:]*([A-Z0-9\\-]+)"
#   ]
# }
