"""Main PDF2Excel processor application."""

import time
from pathlib import Path
from typing import Optional, Dict, Any

from .core.pdf_extractor import PDFExtractor
from .core.field_mapper import FieldMapper
from .core.data_validator import DataValidator
from .core.excel_generator import ExcelGenerator
from .models.order_data import ProcessingResult, ProcessingStatus
from .utils.config import Config
from .utils.logger import setup_logger, get_logger


class PDF2ExcelProcessor:
    """Main processor for converting PDF order confirmations to Excel."""
    
    def __init__(self, config: Optional[Config] = None):
        """
        Initialize the PDF2Excel processor.
        
        Args:
            config: Configuration object (uses default if None)
        """
        self.config = config or Config()
        
        # Setup logging
        setup_logger(
            log_level=self.config.log_level,
            log_file=self.config.log_file
        )
        self.logger = get_logger(__name__)
        
        # Initialize components
        self.pdf_extractor = PDFExtractor(self.config)
        self.field_mapper = FieldMapper(self.config)
        self.data_validator = DataValidator(self.config)
        self.excel_generator = ExcelGenerator(self.config)
        
        self.logger.info("PDF2Excel processor initialized")
    
    def process_pdf(self, pdf_path: str, output_path: Optional[str] = None) -> ProcessingResult:
        """
        Process a PDF file and generate Excel output.
        
        Args:
            pdf_path: Path to input PDF file
            output_path: Path for output Excel file (auto-generated if None)
            
        Returns:
            ProcessingResult with processing status and details
        """
        start_time = time.time()
        pdf_path = Path(pdf_path)
        
        self.logger.info(f"Starting PDF processing: {pdf_path}")
        
        if not pdf_path.exists():
            error_msg = f"PDF file not found: {pdf_path}"
            self.logger.error(error_msg)
            return ProcessingResult(
                status=ProcessingStatus.ERROR,
                input_file=str(pdf_path),
                processing_time=0.0,
                errors=[error_msg]
            )
        
        try:
            # Step 1: Extract content from PDF
            self.logger.info("Step 1: Extracting PDF content")
            extraction_contexts = self.pdf_extractor.extract_content(str(pdf_path))
            
            if not extraction_contexts:
                error_msg = "No content extracted from PDF"
                self.logger.error(error_msg)
                return ProcessingResult(
                    status=ProcessingStatus.ERROR,
                    input_file=str(pdf_path),
                    processing_time=time.time() - start_time,
                    pages_processed=0,
                    errors=[error_msg]
                )
            
            # Step 2: Map fields to structured data
            self.logger.info("Step 2: Mapping fields to structured data")
            orders = self.field_mapper.map_fields(extraction_contexts)
            
            if not orders:
                error_msg = "No orders could be extracted from PDF"
                self.logger.error(error_msg)
                return ProcessingResult(
                    status=ProcessingStatus.ERROR,
                    input_file=str(pdf_path),
                    processing_time=time.time() - start_time,
                    pages_processed=len(extraction_contexts),
                    ocr_used=any(ctx.extraction_method == "ocr" for ctx in extraction_contexts),
                    errors=[error_msg]
                )
            
            # Step 3: Validate and clean data
            self.logger.info("Step 3: Validating and cleaning data")
            validation_result = self.data_validator.validate_orders(orders)
            
            if not validation_result.orders:
                error_msg = "No valid orders after validation"
                self.logger.error(error_msg)
                validation_result.input_file = str(pdf_path)
                validation_result.processing_time = time.time() - start_time
                validation_result.pages_processed = len(extraction_contexts)
                validation_result.ocr_used = any(ctx.extraction_method == "ocr" for ctx in extraction_contexts)
                validation_result.errors.append(error_msg)
                return validation_result
            
            # Step 4: Generate Excel output
            self.logger.info("Step 4: Generating Excel output")
            if not output_path:
                output_path = self._generate_output_path(pdf_path)
            
            excel_file = self.excel_generator.generate_excel(validation_result, output_path)
            
            # Update result with final information
            processing_time = time.time() - start_time
            validation_result.input_file = str(pdf_path)
            validation_result.output_file = excel_file
            validation_result.processing_time = processing_time
            validation_result.pages_processed = len(extraction_contexts)
            validation_result.ocr_used = any(ctx.extraction_method == "ocr" for ctx in extraction_contexts)
            
            self.logger.info(f"Processing completed successfully in {processing_time:.2f} seconds")
            self.logger.info(f"Output file: {excel_file}")
            
            return validation_result
            
        except Exception as e:
            error_msg = f"Unexpected error during processing: {e}"
            self.logger.error(error_msg, exc_info=True)
            
            return ProcessingResult(
                status=ProcessingStatus.ERROR,
                input_file=str(pdf_path),
                processing_time=time.time() - start_time,
                pages_processed=len(extraction_contexts) if 'extraction_contexts' in locals() else 0,
                ocr_used=False,
                errors=[error_msg]
            )
    
    def process_multiple_pdfs(self, pdf_directory: str, output_directory: str) -> Dict[str, ProcessingResult]:
        """
        Process multiple PDF files in a directory.
        
        Args:
            pdf_directory: Directory containing PDF files
            output_directory: Directory for output Excel files
            
        Returns:
            Dictionary mapping PDF filenames to ProcessingResults
        """
        pdf_dir = Path(pdf_directory)
        output_dir = Path(output_directory)
        
        if not pdf_dir.exists():
            raise FileNotFoundError(f"PDF directory not found: {pdf_dir}")
        
        output_dir.mkdir(parents=True, exist_ok=True)
        
        pdf_files = list(pdf_dir.glob("*.pdf"))
        self.logger.info(f"Found {len(pdf_files)} PDF files to process")
        
        results = {}
        
        for pdf_file in pdf_files:
            self.logger.info(f"Processing: {pdf_file.name}")
            
            output_file = output_dir / f"{pdf_file.stem}_sap_import.xlsx"
            result = self.process_pdf(str(pdf_file), str(output_file))
            
            results[pdf_file.name] = result
            
            # Log result summary
            if result.status == ProcessingStatus.SUCCESS:
                self.logger.info(f"✓ {pdf_file.name}: {result.total_orders} orders, {result.total_line_items} items")
            elif result.status == ProcessingStatus.WARNING:
                self.logger.warning(f"⚠ {pdf_file.name}: {result.total_orders} orders with warnings")
            else:
                self.logger.error(f"✗ {pdf_file.name}: Processing failed")
        
        # Generate batch summary
        self._generate_batch_summary(results, output_dir)
        
        return results
    
    def _generate_output_path(self, pdf_path: Path) -> str:
        """Generate output Excel file path based on input PDF path."""
        output_dir = pdf_path.parent / "output"
        output_dir.mkdir(exist_ok=True)
        
        output_filename = f"{pdf_path.stem}_sap_import.xlsx"
        return str(output_dir / output_filename)
    
    def _generate_batch_summary(self, results: Dict[str, ProcessingResult], output_dir: Path) -> None:
        """Generate summary report for batch processing."""
        summary_file = output_dir / "batch_processing_summary.txt"
        
        total_files = len(results)
        successful = sum(1 for r in results.values() if r.status == ProcessingStatus.SUCCESS)
        warnings = sum(1 for r in results.values() if r.status == ProcessingStatus.WARNING)
        errors = sum(1 for r in results.values() if r.status == ProcessingStatus.ERROR)
        
        total_orders = sum(r.total_orders for r in results.values())
        total_items = sum(r.total_line_items for r in results.values())
        total_time = sum(r.processing_time for r in results.values())
        
        with open(summary_file, 'w', encoding='utf-8') as f:
            f.write("PDF2Excel Batch Processing Summary\n")
            f.write("=" * 50 + "\n\n")
            
            f.write(f"Files Processed: {total_files}\n")
            f.write(f"Successful: {successful}\n")
            f.write(f"With Warnings: {warnings}\n")
            f.write(f"Failed: {errors}\n\n")
            
            f.write(f"Total Orders: {total_orders}\n")
            f.write(f"Total Line Items: {total_items}\n")
            f.write(f"Total Processing Time: {total_time:.2f} seconds\n\n")
            
            f.write("Individual File Results:\n")
            f.write("-" * 30 + "\n")
            
            for filename, result in results.items():
                status_symbol = {
                    ProcessingStatus.SUCCESS: "✓",
                    ProcessingStatus.WARNING: "⚠",
                    ProcessingStatus.ERROR: "✗"
                }[result.status]
                
                f.write(f"{status_symbol} {filename}\n")
                f.write(f"  Status: {result.status.value}\n")
                f.write(f"  Orders: {result.total_orders}\n")
                f.write(f"  Items: {result.total_line_items}\n")
                f.write(f"  Time: {result.processing_time:.2f}s\n")
                
                if result.warnings:
                    f.write(f"  Warnings: {len(result.warnings)}\n")
                if result.errors:
                    f.write(f"  Errors: {len(result.errors)}\n")
                
                f.write("\n")
        
        self.logger.info(f"Batch summary saved: {summary_file}")
    
    def get_processing_stats(self) -> Dict[str, Any]:
        """Get processing statistics and configuration info."""
        return {
            "version": "1.0.0",
            "config": {
                "ocr_language": self.config.ocr_language,
                "valid_currencies": self.config.valid_currencies,
                "date_format": self.config.date_format,
                "decimal_places": self.config.decimal_places
            },
            "components": {
                "pdf_extractor": type(self.pdf_extractor).__name__,
                "field_mapper": type(self.field_mapper).__name__,
                "data_validator": type(self.data_validator).__name__,
                "excel_generator": type(self.excel_generator).__name__
            }
        }
