"""Configuration management for PDF2Excel."""

from typing import List, Dict, Any
from pydantic import BaseSettings, Field
from pathlib import Path


class Config(BaseSettings):
    """Application configuration."""
    
    # OCR Settings
    tesseract_cmd: str = Field(default="tesseract", description="Path to tesseract executable")
    ocr_language: str = Field(default="eng", description="OCR language code")
    ocr_dpi: int = Field(default=300, description="DPI for PDF to image conversion")
    
    # PDF Processing
    pdf_password: str = Field(default="", description="Default PDF password if needed")
    extract_images: bool = Field(default=True, description="Whether to extract images for OCR")
    
    # Field Mapping
    field_patterns: Dict[str, List[str]] = Field(
        default={
            "order_number": [
                r"order\s*(?:number|no|#)[\s:]*([A-Z0-9\-]+)",
                r"po\s*(?:number|no|#)[\s:]*([A-Z0-9\-]+)",
                r"purchase\s*order[\s:]*([A-Z0-9\-]+)"
            ],
            "customer_code": [
                r"customer\s*(?:code|id|number)[\s:]*([A-Z0-9\-]+)",
                r"client\s*(?:code|id|number)[\s:]*([A-Z0-9\-]+)",
                r"account\s*(?:code|id|number)[\s:]*([A-Z0-9\-]+)"
            ],
            "delivery_date": [
                r"delivery\s*date[\s:]*(\d{1,2}[\/\-\.]\d{1,2}[\/\-\.]\d{2,4})",
                r"ship\s*date[\s:]*(\d{1,2}[\/\-\.]\d{1,2}[\/\-\.]\d{2,4})",
                r"expected\s*date[\s:]*(\d{1,2}[\/\-\.]\d{1,2}[\/\-\.]\d{2,4})"
            ],
            "material_code": [
                r"(?:item|product|material)\s*(?:code|number|id)[\s:]*([A-Z0-9\-]+)",
                r"sku[\s:]*([A-Z0-9\-]+)",
                r"part\s*(?:number|no)[\s:]*([A-Z0-9\-]+)"
            ]
        },
        description="Regex patterns for field extraction"
    )
    
    # Data Validation
    min_quantity: float = Field(default=0.01, description="Minimum valid quantity")
    max_quantity: float = Field(default=999999.99, description="Maximum valid quantity")
    valid_currencies: List[str] = Field(
        default=["USD", "EUR", "GBP", "CAD", "AUD"],
        description="List of valid currency codes"
    )
    
    # Excel Output
    excel_sheet_name: str = Field(default="SAP_Import", description="Excel sheet name")
    date_format: str = Field(default="YYYY.MM.DD", description="Date format for SAP")
    decimal_places: int = Field(default=2, description="Decimal places for prices")
    
    # Logging
    log_level: str = Field(default="INFO", description="Logging level")
    log_file: str = Field(default="pdf2excel.log", description="Log file name")
    
    class Config:
        env_prefix = "PDF2EXCEL_"
        case_sensitive = False
