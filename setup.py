"""Setup script for PDF2Excel package."""

from setuptools import setup, find_packages
from pathlib import Path

# Read README file
readme_file = Path(__file__).parent / "README.md"
long_description = readme_file.read_text(encoding="utf-8") if readme_file.exists() else ""

# Read requirements
requirements_file = Path(__file__).parent / "requirements.txt"
requirements = []
if requirements_file.exists():
    requirements = requirements_file.read_text(encoding="utf-8").strip().split("\n")
    requirements = [req.strip() for req in requirements if req.strip() and not req.startswith("#")]

setup(
    name="pdf2excel",
    version="1.0.0",
    author="PDF2Excel Team",
    author_email="<EMAIL>",
    description="Convert PDF order confirmations to SAP-ready Excel files",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/your-org/pdf2excel",
    package_dir={"": "src"},
    packages=find_packages(where="src"),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Healthcare Industry",
        "Intended Audience :: Manufacturing",
        "Topic :: Office/Business",
        "Topic :: Text Processing :: Markup",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Operating System :: OS Independent",
    ],
    python_requires=">=3.8",
    install_requires=requirements,
    extras_require={
        "dev": [
            "pytest>=7.4.3",
            "pytest-cov>=4.1.0",
            "black>=23.0.0",
            "flake8>=6.0.0",
            "mypy>=1.0.0",
        ],
        "docs": [
            "sphinx>=7.0.0",
            "sphinx-rtd-theme>=1.3.0",
        ],
    },
    entry_points={
        "console_scripts": [
            "pdf2excel=pdf2excel.cli:main",
        ],
    },
    include_package_data=True,
    zip_safe=False,
    keywords="pdf excel sap pharmaceutical order processing ocr",
    project_urls={
        "Bug Reports": "https://github.com/your-org/pdf2excel/issues",
        "Source": "https://github.com/your-org/pdf2excel",
        "Documentation": "https://pdf2excel.readthedocs.io/",
    },
)
