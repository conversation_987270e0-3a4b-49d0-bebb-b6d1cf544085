"""Data validation and cleaning for pharmaceutical orders."""

import re
from typing import List, Dict, Any, <PERSON><PERSON>, Optional
from datetime import datetime, timedelta
from decimal import Decimal, InvalidOperation

from ..models.order_data import OrderData, OrderLineItem, ProcessingResult, ProcessingStatus
from ..utils.logger import get_logger
from ..utils.config import Config

logger = get_logger(__name__)


class DataValidator:
    """Validate and clean order data for SAP import."""
    
    def __init__(self, config: Config):
        """Initialize validator with configuration."""
        self.config = config
        self.validation_rules = self._setup_validation_rules()
    
    def _setup_validation_rules(self) -> Dict[str, Any]:
        """Set up validation rules based on configuration."""
        return {
            'material_code': {
                'pattern': r'^[A-Z0-9\-_]+$',
                'min_length': 1,
                'max_length': 50
            },
            'quantity': {
                'min_value': self.config.min_quantity,
                'max_value': self.config.max_quantity,
                'decimal_places': 3
            },
            'net_price': {
                'min_value': Decimal('0.00'),
                'max_value': Decimal('999999.99'),
                'decimal_places': self.config.decimal_places
            },
            'currency': {
                'valid_codes': self.config.valid_currencies,
                'pattern': r'^[A-Z]{3}$'
            },
            'discount_percent': {
                'min_value': Decimal('0.00'),
                'max_value': Decimal('100.00'),
                'decimal_places': 2
            },
            'delivery_date': {
                'min_date': datetime.now().date(),
                'max_date': (datetime.now() + timedelta(days=365)).date()
            }
        }
    
    def validate_orders(self, orders: List[OrderData]) -> ProcessingResult:
        """
        Validate list of orders and return processing result.
        
        Args:
            orders: List of order data to validate
            
        Returns:
            ProcessingResult with validation status and issues
        """
        logger.info(f"Validating {len(orders)} orders")
        
        validated_orders = []
        warnings = []
        errors = []
        
        for order_idx, order in enumerate(orders):
            try:
                validated_order, order_warnings, order_errors = self._validate_single_order(order, order_idx)
                
                if validated_order:
                    validated_orders.append(validated_order)
                
                warnings.extend(order_warnings)
                errors.extend(order_errors)
                
            except Exception as e:
                error_msg = f"Critical validation error for order {order_idx}: {e}"
                logger.error(error_msg)
                errors.append(error_msg)
        
        # Determine overall status
        if errors:
            status = ProcessingStatus.ERROR
        elif warnings:
            status = ProcessingStatus.WARNING
        else:
            status = ProcessingStatus.SUCCESS
        
        result = ProcessingResult(
            status=status,
            orders=validated_orders,
            input_file="",  # Will be set by main processor
            processing_time=0.0,  # Will be set by main processor
            warnings=warnings,
            errors=errors
        )
        
        logger.info(f"Validation complete: {len(validated_orders)} valid orders, "
                   f"{len(warnings)} warnings, {len(errors)} errors")
        
        return result
    
    def _validate_single_order(self, order: OrderData, order_idx: int) -> Tuple[Optional[OrderData], List[str], List[str]]:
        """Validate a single order and return cleaned version with issues."""
        warnings = []
        errors = []
        
        try:
            # Validate order-level fields
            order_warnings, order_errors = self._validate_order_fields(order, order_idx)
            warnings.extend(order_warnings)
            errors.extend(order_errors)
            
            # Validate and clean line items
            validated_line_items = []
            for item_idx, line_item in enumerate(order.line_items):
                validated_item, item_warnings, item_errors = self._validate_line_item(
                    line_item, order_idx, item_idx
                )
                
                if validated_item:
                    validated_line_items.append(validated_item)
                
                warnings.extend(item_warnings)
                errors.extend(item_errors)
            
            if not validated_line_items:
                errors.append(f"Order {order_idx}: No valid line items found")
                return None, warnings, errors
            
            # Create validated order
            validated_order = OrderData(
                order_number=order.order_number,
                customer_code=order.customer_code,
                delivery_date=order.delivery_date,
                line_items=validated_line_items,
                order_date=order.order_date,
                customer_name=order.customer_name,
                total_amount=order.total_amount,
                remarks=order.remarks
            )
            
            return validated_order, warnings, errors
            
        except Exception as e:
            errors.append(f"Order {order_idx}: Validation failed - {e}")
            return None, warnings, errors
    
    def _validate_order_fields(self, order: OrderData, order_idx: int) -> Tuple[List[str], List[str]]:
        """Validate order-level fields."""
        warnings = []
        errors = []
        
        # Validate order number
        if not order.order_number or not order.order_number.strip():
            errors.append(f"Order {order_idx}: Order number is required")
        elif len(order.order_number) > 50:
            warnings.append(f"Order {order_idx}: Order number is very long ({len(order.order_number)} chars)")
        
        # Validate customer code
        if not order.customer_code or not order.customer_code.strip():
            errors.append(f"Order {order_idx}: Customer code is required")
        elif not re.match(r'^[A-Z0-9\-_]+$', order.customer_code):
            warnings.append(f"Order {order_idx}: Customer code contains special characters")
        
        # Validate delivery date
        if order.delivery_date:
            delivery_date = order.delivery_date.date() if isinstance(order.delivery_date, datetime) else order.delivery_date
            rules = self.validation_rules['delivery_date']
            
            if delivery_date < rules['min_date']:
                warnings.append(f"Order {order_idx}: Delivery date is in the past")
            elif delivery_date > rules['max_date']:
                warnings.append(f"Order {order_idx}: Delivery date is more than 1 year in the future")
        
        return warnings, errors
    
    def _validate_line_item(self, line_item: OrderLineItem, order_idx: int, 
                           item_idx: int) -> Tuple[Optional[OrderLineItem], List[str], List[str]]:
        """Validate and clean a single line item."""
        warnings = []
        errors = []
        
        try:
            # Validate material code
            material_code = self._validate_material_code(line_item.material_code, order_idx, item_idx)
            if not material_code:
                errors.append(f"Order {order_idx}, Item {item_idx}: Invalid material code")
                return None, warnings, errors
            
            # Validate quantity
            quantity, qty_warnings, qty_errors = self._validate_quantity(
                line_item.quantity, order_idx, item_idx
            )
            warnings.extend(qty_warnings)
            errors.extend(qty_errors)
            
            if quantity is None:
                return None, warnings, errors
            
            # Validate unit of measure
            unit_of_measure = self._validate_unit_of_measure(line_item.unit_of_measure)
            
            # Validate net price
            net_price, price_warnings, price_errors = self._validate_net_price(
                line_item.net_price, order_idx, item_idx
            )
            warnings.extend(price_warnings)
            errors.extend(price_errors)
            
            if net_price is None:
                net_price = Decimal('0.00')
                warnings.append(f"Order {order_idx}, Item {item_idx}: Net price set to 0.00")
            
            # Validate currency
            currency = self._validate_currency(line_item.currency, order_idx, item_idx)
            if currency != line_item.currency:
                warnings.append(f"Order {order_idx}, Item {item_idx}: Currency corrected to {currency}")
            
            # Validate discount
            discount_percent = None
            if line_item.discount_percent is not None:
                discount_percent, disc_warnings, disc_errors = self._validate_discount(
                    line_item.discount_percent, order_idx, item_idx
                )
                warnings.extend(disc_warnings)
                errors.extend(disc_errors)
            
            # Create validated line item
            validated_item = OrderLineItem(
                material_code=material_code,
                quantity=quantity,
                unit_of_measure=unit_of_measure,
                net_price=net_price,
                currency=currency,
                discount_percent=discount_percent,
                line_total=line_item.line_total,
                remarks=line_item.remarks
            )
            
            return validated_item, warnings, errors
            
        except Exception as e:
            errors.append(f"Order {order_idx}, Item {item_idx}: Validation error - {e}")
            return None, warnings, errors
    
    def _validate_material_code(self, material_code: str, order_idx: int, item_idx: int) -> Optional[str]:
        """Validate and clean material code."""
        if not material_code:
            return None
        
        # Clean the material code
        cleaned = re.sub(r'[^\w\-]', '', material_code.upper().strip())
        
        rules = self.validation_rules['material_code']
        
        if len(cleaned) < rules['min_length']:
            return None
        
        if len(cleaned) > rules['max_length']:
            cleaned = cleaned[:rules['max_length']]
        
        if not re.match(rules['pattern'], cleaned):
            return None
        
        return cleaned
    
    def _validate_quantity(self, quantity: Decimal, order_idx: int, 
                          item_idx: int) -> Tuple[Optional[Decimal], List[str], List[str]]:
        """Validate quantity value."""
        warnings = []
        errors = []
        
        if quantity is None:
            errors.append(f"Order {order_idx}, Item {item_idx}: Quantity is required")
            return None, warnings, errors
        
        rules = self.validation_rules['quantity']
        
        if quantity < rules['min_value']:
            errors.append(f"Order {order_idx}, Item {item_idx}: Quantity too small ({quantity})")
            return None, warnings, errors
        
        if quantity > rules['max_value']:
            warnings.append(f"Order {order_idx}, Item {item_idx}: Very large quantity ({quantity})")
        
        # Round to specified decimal places
        try:
            rounded_quantity = quantity.quantize(Decimal('0.001'))  # 3 decimal places
            if rounded_quantity != quantity:
                warnings.append(f"Order {order_idx}, Item {item_idx}: Quantity rounded to 3 decimal places")
            return rounded_quantity, warnings, errors
        except InvalidOperation:
            errors.append(f"Order {order_idx}, Item {item_idx}: Invalid quantity format")
            return None, warnings, errors
    
    def _validate_net_price(self, net_price: Decimal, order_idx: int, 
                           item_idx: int) -> Tuple[Optional[Decimal], List[str], List[str]]:
        """Validate net price value."""
        warnings = []
        errors = []
        
        if net_price is None:
            warnings.append(f"Order {order_idx}, Item {item_idx}: Net price is missing")
            return Decimal('0.00'), warnings, errors
        
        rules = self.validation_rules['net_price']
        
        if net_price < rules['min_value']:
            warnings.append(f"Order {order_idx}, Item {item_idx}: Net price is zero or negative")
        
        if net_price > rules['max_value']:
            warnings.append(f"Order {order_idx}, Item {item_idx}: Very high net price ({net_price})")
        
        # Round to specified decimal places
        try:
            decimal_places = '0.' + '0' * rules['decimal_places']
            rounded_price = net_price.quantize(Decimal(decimal_places))
            if rounded_price != net_price:
                warnings.append(f"Order {order_idx}, Item {item_idx}: Price rounded to {rules['decimal_places']} decimal places")
            return rounded_price, warnings, errors
        except InvalidOperation:
            errors.append(f"Order {order_idx}, Item {item_idx}: Invalid price format")
            return None, warnings, errors
    
    def _validate_currency(self, currency: str, order_idx: int, item_idx: int) -> str:
        """Validate and correct currency code."""
        if not currency:
            return 'USD'  # Default currency
        
        currency_upper = currency.upper().strip()
        rules = self.validation_rules['currency']
        
        if currency_upper in rules['valid_codes']:
            return currency_upper
        
        # Try to match common variations
        currency_mapping = {
            'DOLLAR': 'USD', 'DOLLARS': 'USD', '$': 'USD',
            'EURO': 'EUR', 'EUROS': 'EUR', '€': 'EUR',
            'POUND': 'GBP', 'POUNDS': 'GBP', '£': 'GBP'
        }
        
        return currency_mapping.get(currency_upper, 'USD')
    
    def _validate_unit_of_measure(self, unit: str) -> str:
        """Validate and standardize unit of measure."""
        if not unit:
            return 'EA'  # Default unit
        
        unit_upper = unit.upper().strip()
        
        # Standardize common units
        unit_mapping = {
            'EACH': 'EA', 'PIECE': 'EA', 'PIECES': 'EA', 'PC': 'EA', 'PCS': 'EA',
            'KILOGRAM': 'KG', 'KILOGRAMS': 'KG', 'KILO': 'KG', 'KILOS': 'KG',
            'GRAM': 'G', 'GRAMS': 'G', 'GR': 'G', 'GRS': 'G',
            'LITER': 'L', 'LITERS': 'L', 'LITRE': 'L', 'LITRES': 'L', 'LTR': 'L',
            'MILLILITER': 'ML', 'MILLILITERS': 'ML', 'MILLILITRE': 'ML', 'MILLILITRES': 'ML'
        }
        
        return unit_mapping.get(unit_upper, unit_upper)
    
    def _validate_discount(self, discount: Decimal, order_idx: int, 
                          item_idx: int) -> Tuple[Optional[Decimal], List[str], List[str]]:
        """Validate discount percentage."""
        warnings = []
        errors = []
        
        rules = self.validation_rules['discount_percent']
        
        if discount < rules['min_value']:
            warnings.append(f"Order {order_idx}, Item {item_idx}: Negative discount ({discount}%)")
            return Decimal('0.00'), warnings, errors
        
        if discount > rules['max_value']:
            warnings.append(f"Order {order_idx}, Item {item_idx}: Discount over 100% ({discount}%)")
            return rules['max_value'], warnings, errors
        
        # Round to specified decimal places
        try:
            decimal_places = '0.' + '0' * rules['decimal_places']
            rounded_discount = discount.quantize(Decimal(decimal_places))
            return rounded_discount, warnings, errors
        except InvalidOperation:
            errors.append(f"Order {order_idx}, Item {item_idx}: Invalid discount format")
            return None, warnings, errors
