"""Data models for order processing."""

from typing import List, Optional, Dict, Any
from datetime import datetime
from decimal import Decimal
from pydantic import BaseModel, Field, validator
from enum import Enum


class ProcessingStatus(str, Enum):
    """Processing status enumeration."""
    SUCCESS = "success"
    WARNING = "warning"
    ERROR = "error"


class OrderLineItem(BaseModel):
    """Individual line item in an order."""
    
    material_code: str = Field(..., description="Material/Product code")
    quantity: Decimal = Field(..., description="Ordered quantity")
    unit_of_measure: str = Field(..., description="Unit of measure (e.g., EA, KG, L)")
    net_price: Decimal = Field(..., description="Net price per unit")
    currency: str = Field(default="USD", description="Currency code")
    discount_percent: Optional[Decimal] = Field(default=None, description="Discount percentage")
    line_total: Optional[Decimal] = Field(default=None, description="Line total amount")
    remarks: Optional[str] = Field(default=None, description="Any remarks or issues")
    
    @validator('material_code')
    def validate_material_code(cls, v):
        """Ensure material code is alphanumeric without spaces."""
        if not v or not v.replace('-', '').replace('_', '').isalnum():
            raise ValueError("Material code must be alphanumeric (hyphens and underscores allowed)")
        return v.upper().strip()
    
    @validator('quantity')
    def validate_quantity(cls, v):
        """Ensure quantity is positive."""
        if v <= 0:
            raise ValueError("Quantity must be positive")
        return v
    
    @validator('currency')
    def validate_currency(cls, v):
        """Ensure currency is a valid 3-letter code."""
        if len(v) != 3 or not v.isalpha():
            raise ValueError("Currency must be a 3-letter code")
        return v.upper()


class OrderData(BaseModel):
    """Complete order data structure."""
    
    order_number: str = Field(..., description="Order number")
    customer_code: str = Field(..., description="Customer code")
    delivery_date: datetime = Field(..., description="Delivery date")
    line_items: List[OrderLineItem] = Field(..., description="Order line items")
    
    # Optional fields
    order_date: Optional[datetime] = Field(default=None, description="Order date")
    customer_name: Optional[str] = Field(default=None, description="Customer name")
    total_amount: Optional[Decimal] = Field(default=None, description="Total order amount")
    remarks: Optional[str] = Field(default=None, description="Order-level remarks")
    
    @validator('order_number', 'customer_code')
    def validate_codes(cls, v):
        """Ensure codes are not empty and trimmed."""
        if not v or not v.strip():
            raise ValueError("Code cannot be empty")
        return v.strip().upper()
    
    @validator('line_items')
    def validate_line_items(cls, v):
        """Ensure at least one line item exists."""
        if not v:
            raise ValueError("Order must have at least one line item")
        return v


class ProcessingResult(BaseModel):
    """Result of PDF processing operation."""
    
    status: ProcessingStatus = Field(..., description="Processing status")
    orders: List[OrderData] = Field(default_factory=list, description="Extracted orders")
    output_file: Optional[str] = Field(default=None, description="Path to generated Excel file")
    
    # Processing metadata
    input_file: str = Field(..., description="Input PDF file path")
    processing_time: float = Field(..., description="Processing time in seconds")
    pages_processed: int = Field(default=0, description="Number of pages processed")
    ocr_used: bool = Field(default=False, description="Whether OCR was used")
    
    # Issues and warnings
    warnings: List[str] = Field(default_factory=list, description="Processing warnings")
    errors: List[str] = Field(default_factory=list, description="Processing errors")
    
    @property
    def total_orders(self) -> int:
        """Total number of orders processed."""
        return len(self.orders)
    
    @property
    def total_line_items(self) -> int:
        """Total number of line items across all orders."""
        return sum(len(order.line_items) for order in self.orders)
    
    @property
    def has_issues(self) -> bool:
        """Whether there were any warnings or errors."""
        return bool(self.warnings or self.errors)


class ExtractionContext(BaseModel):
    """Context information for PDF extraction."""
    
    file_path: str = Field(..., description="Path to PDF file")
    page_number: int = Field(..., description="Current page number")
    extraction_method: str = Field(..., description="Extraction method used")
    confidence_score: Optional[float] = Field(default=None, description="OCR confidence score")
    raw_text: str = Field(..., description="Raw extracted text")
    tables: List[Dict[str, Any]] = Field(default_factory=list, description="Extracted tables")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")
