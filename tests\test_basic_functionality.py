"""Basic functionality tests for PDF2Excel."""

import pytest
import sys
from pathlib import Path
from datetime import datetime
from decimal import Decimal

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from pdf2excel import PDF2ExcelProcessor
from pdf2excel.utils.config import Config
from pdf2excel.models.order_data import OrderData, OrderLineItem, ProcessingStatus


class TestPDF2ExcelProcessor:
    """Test the main PDF2Excel processor."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.config = Config(log_level="ERROR")  # Suppress logs during testing
        self.processor = PDF2ExcelProcessor(self.config)
    
    def test_processor_initialization(self):
        """Test processor initializes correctly."""
        assert self.processor is not None
        assert self.processor.config is not None
        assert self.processor.pdf_extractor is not None
        assert self.processor.field_mapper is not None
        assert self.processor.data_validator is not None
        assert self.processor.excel_generator is not None
    
    def test_process_nonexistent_file(self):
        """Test processing non-existent PDF file."""
        result = self.processor.process_pdf("nonexistent.pdf")
        
        assert result.status == ProcessingStatus.ERROR
        assert len(result.errors) > 0
        assert "not found" in result.errors[0].lower()
        assert result.total_orders == 0
    
    def test_get_processing_stats(self):
        """Test getting processing statistics."""
        stats = self.processor.get_processing_stats()
        
        assert "version" in stats
        assert "config" in stats
        assert "components" in stats
        
        # Check config values
        config_stats = stats["config"]
        assert "ocr_language" in config_stats
        assert "valid_currencies" in config_stats
        assert "date_format" in config_stats
        assert "decimal_places" in config_stats
        
        # Check components
        components = stats["components"]
        assert "pdf_extractor" in components
        assert "field_mapper" in components
        assert "data_validator" in components
        assert "excel_generator" in components


class TestOrderDataModels:
    """Test order data models."""
    
    def test_order_line_item_creation(self):
        """Test creating a valid order line item."""
        item = OrderLineItem(
            material_code="TEST-001",
            quantity=Decimal("10.500"),
            unit_of_measure="EA",
            net_price=Decimal("15.99"),
            currency="USD"
        )
        
        assert item.material_code == "TEST-001"
        assert item.quantity == Decimal("10.500")
        assert item.unit_of_measure == "EA"
        assert item.net_price == Decimal("15.99")
        assert item.currency == "USD"
    
    def test_order_line_item_validation(self):
        """Test order line item validation."""
        # Test invalid material code
        with pytest.raises(ValueError):
            OrderLineItem(
                material_code="",  # Empty material code
                quantity=Decimal("10.0"),
                unit_of_measure="EA",
                net_price=Decimal("15.99")
            )
        
        # Test invalid quantity
        with pytest.raises(ValueError):
            OrderLineItem(
                material_code="TEST-001",
                quantity=Decimal("-5.0"),  # Negative quantity
                unit_of_measure="EA",
                net_price=Decimal("15.99")
            )
        
        # Test invalid currency
        with pytest.raises(ValueError):
            OrderLineItem(
                material_code="TEST-001",
                quantity=Decimal("10.0"),
                unit_of_measure="EA",
                net_price=Decimal("15.99"),
                currency="INVALID"  # Invalid currency code
            )
    
    def test_order_data_creation(self):
        """Test creating a valid order."""
        line_items = [
            OrderLineItem(
                material_code="TEST-001",
                quantity=Decimal("10.0"),
                unit_of_measure="EA",
                net_price=Decimal("15.99"),
                currency="USD"
            )
        ]
        
        order = OrderData(
            order_number="PO-2024-001",
            customer_code="CUST-001",
            delivery_date=datetime(2024, 12, 15),
            line_items=line_items
        )
        
        assert order.order_number == "PO-2024-001"
        assert order.customer_code == "CUST-001"
        assert len(order.line_items) == 1
        assert order.line_items[0].material_code == "TEST-001"
    
    def test_order_data_validation(self):
        """Test order data validation."""
        # Test empty line items
        with pytest.raises(ValueError):
            OrderData(
                order_number="PO-2024-001",
                customer_code="CUST-001",
                delivery_date=datetime(2024, 12, 15),
                line_items=[]  # Empty line items
            )


class TestDataValidator:
    """Test data validation functionality."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.config = Config(log_level="ERROR")
        self.processor = PDF2ExcelProcessor(self.config)
        self.validator = self.processor.data_validator
    
    def test_validate_valid_orders(self):
        """Test validation of valid orders."""
        line_items = [
            OrderLineItem(
                material_code="ASPIRIN-100MG",
                quantity=Decimal("100.0"),
                unit_of_measure="EA",
                net_price=Decimal("0.15"),
                currency="USD"
            )
        ]
        
        orders = [
            OrderData(
                order_number="PO-2024-001",
                customer_code="CUST-001",
                delivery_date=datetime(2024, 12, 15),
                line_items=line_items
            )
        ]
        
        result = self.validator.validate_orders(orders)
        
        assert result.status in [ProcessingStatus.SUCCESS, ProcessingStatus.WARNING]
        assert len(result.orders) == 1
        assert result.orders[0].order_number == "PO-2024-001"
    
    def test_validate_material_code_cleaning(self):
        """Test material code cleaning and validation."""
        # Test cleaning special characters
        cleaned = self.validator._validate_material_code("TEST@#$001", 0, 0)
        assert cleaned == "TEST001"
        
        # Test empty material code
        cleaned = self.validator._validate_material_code("", 0, 0)
        assert cleaned is None
        
        # Test very long material code
        long_code = "A" * 100
        cleaned = self.validator._validate_material_code(long_code, 0, 0)
        assert len(cleaned) <= 50  # Should be truncated
    
    def test_validate_quantity(self):
        """Test quantity validation."""
        # Valid quantity
        qty, warnings, errors = self.validator._validate_quantity(Decimal("10.500"), 0, 0)
        assert qty == Decimal("10.500")
        assert len(errors) == 0
        
        # Invalid quantity (too small)
        qty, warnings, errors = self.validator._validate_quantity(Decimal("0.0"), 0, 0)
        assert qty is None
        assert len(errors) > 0
        
        # Very large quantity (should generate warning)
        qty, warnings, errors = self.validator._validate_quantity(Decimal("999999.0"), 0, 0)
        assert qty == Decimal("999999.0")
        assert len(warnings) > 0
    
    def test_validate_currency(self):
        """Test currency validation and correction."""
        # Valid currency
        currency = self.validator._validate_currency("USD", 0, 0)
        assert currency == "USD"
        
        # Invalid currency (should default to USD)
        currency = self.validator._validate_currency("INVALID", 0, 0)
        assert currency == "USD"
        
        # Currency mapping
        currency = self.validator._validate_currency("DOLLAR", 0, 0)
        assert currency == "USD"
        
        currency = self.validator._validate_currency("EURO", 0, 0)
        assert currency == "EUR"


class TestConfig:
    """Test configuration functionality."""
    
    def test_default_config(self):
        """Test default configuration values."""
        config = Config()
        
        assert config.tesseract_cmd == "tesseract"
        assert config.ocr_language == "eng"
        assert config.ocr_dpi == 300
        assert config.min_quantity == 0.01
        assert "USD" in config.valid_currencies
        assert config.decimal_places == 2
    
    def test_custom_config(self):
        """Test custom configuration values."""
        config = Config(
            ocr_language="deu",
            ocr_dpi=400,
            min_quantity=0.001,
            decimal_places=3,
            valid_currencies=["EUR", "GBP"]
        )
        
        assert config.ocr_language == "deu"
        assert config.ocr_dpi == 400
        assert config.min_quantity == 0.001
        assert config.decimal_places == 3
        assert config.valid_currencies == ["EUR", "GBP"]


if __name__ == "__main__":
    # Run tests if executed directly
    pytest.main([__file__, "-v"])
