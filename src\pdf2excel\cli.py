"""Command-line interface module for PDF2Excel."""

# This module provides CLI functionality
# The actual CLI script is in the root directory as cli.py

from .main import PDF2ExcelProcessor
from .utils.config import Config

def main():
    """CLI entry point - delegates to root cli.py"""
    import sys
    from pathlib import Path
    
    # Import and run the main CLI
    cli_path = Path(__file__).parent.parent.parent / "cli.py"
    
    if cli_path.exists():
        # Execute the CLI script
        import subprocess
        result = subprocess.run([sys.executable, str(cli_path)] + sys.argv[1:])
        sys.exit(result.returncode)
    else:
        print("CLI script not found. Please run from the project root directory.")
        sys.exit(1)

if __name__ == "__main__":
    main()
