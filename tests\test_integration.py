"""Integration tests for PDF2Excel system."""

import pytest
import sys
import tempfile
import shutil
from pathlib import Path
from datetime import datetime
from decimal import Decimal

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from pdf2excel import PDF2ExcelProcessor
from pdf2excel.utils.config import Config
from pdf2excel.models.order_data import OrderData, OrderLineItem, ProcessingStatus


class TestIntegration:
    """Integration tests for the complete PDF2Excel system."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.temp_dir = Path(tempfile.mkdtemp())
        self.config = Config(log_level="ERROR")  # Suppress logs during testing
        self.processor = PDF2ExcelProcessor(self.config)
    
    def teardown_method(self):
        """Clean up test fixtures."""
        if self.temp_dir.exists():
            shutil.rmtree(self.temp_dir)
    
    def test_end_to_end_programmatic_data(self):
        """Test complete end-to-end processing with programmatic data."""
        # Create sample order data
        line_items = [
            OrderLineItem(
                material_code="ASPIRIN-100MG",
                quantity=Decimal("500.000"),
                unit_of_measure="EA",
                net_price=Decimal("0.15"),
                currency="USD",
                discount_percent=Decimal("5.00")
            ),
            OrderLineItem(
                material_code="IBUPROFEN-200MG",
                quantity=Decimal("250.000"),
                unit_of_measure="EA",
                net_price=Decimal("0.25"),
                currency="USD",
                discount_percent=Decimal("2.50")
            )
        ]
        
        orders = [
            OrderData(
                order_number="PO-2024-001",
                customer_code="CUST-12345",
                delivery_date=datetime(2024, 12, 15),
                line_items=line_items,
                customer_name="ABC Pharmacy Chain"
            )
        ]
        
        # Validate orders
        validation_result = self.processor.data_validator.validate_orders(orders)
        assert validation_result.status in [ProcessingStatus.SUCCESS, ProcessingStatus.WARNING]
        assert len(validation_result.orders) == 1
        
        # Generate Excel file
        output_file = self.temp_dir / "test_output.xlsx"
        excel_file = self.processor.excel_generator.generate_excel(
            validation_result, 
            str(output_file)
        )
        
        # Verify Excel file was created
        assert Path(excel_file).exists()
        assert Path(excel_file).stat().st_size > 0
        
        # Verify file can be read (basic check)
        import openpyxl
        wb = openpyxl.load_workbook(excel_file)
        assert len(wb.sheetnames) >= 1
        
        # Check main sheet
        ws = wb.active
        assert ws.max_row >= 3  # Header + 2 data rows
        assert ws.max_column >= 9  # At least 9 SAP columns
        
        # Check header row
        headers = [cell.value for cell in ws[1]]
        expected_headers = [
            "Order Number", "Customer Code", "Delivery Date", 
            "Material Code", "Quantity", "Unit of Measure", 
            "Net Price", "Currency", "Discount (%)"
        ]
        for expected_header in expected_headers:
            assert expected_header in headers
        
        # Check data rows
        assert ws.cell(2, 1).value == "PO-2024-001"  # Order Number
        assert ws.cell(2, 2).value == "CUST-12345"   # Customer Code
        assert ws.cell(2, 4).value == "ASPIRIN-100MG"  # Material Code
        
        wb.close()
    
    def test_validation_with_errors(self):
        """Test validation handling with various error conditions."""
        # Create orders with various issues
        problematic_items = [
            OrderLineItem(
                material_code="",  # Empty material code - should fail validation
                quantity=Decimal("10.0"),
                unit_of_measure="EA",
                net_price=Decimal("1.00"),
                currency="USD"
            )
        ]
        
        try:
            # This should raise a validation error during creation
            OrderData(
                order_number="PO-ERROR-001",
                customer_code="CUST-001",
                delivery_date=datetime(2024, 12, 15),
                line_items=problematic_items
            )
            assert False, "Expected validation error for empty material code"
        except ValueError:
            # Expected behavior
            pass
    
    def test_batch_processing_simulation(self):
        """Test batch processing simulation with multiple orders."""
        # Create multiple orders
        orders = []
        for i in range(3):
            line_items = [
                OrderLineItem(
                    material_code=f"PRODUCT-{i:03d}",
                    quantity=Decimal(f"{(i+1)*10}.000"),
                    unit_of_measure="EA",
                    net_price=Decimal(f"{(i+1)*0.5:.2f}"),
                    currency="USD"
                )
            ]
            
            orders.append(OrderData(
                order_number=f"PO-2024-{i+1:03d}",
                customer_code=f"CUST-{i+1:03d}",
                delivery_date=datetime(2024, 12, 15 + i),
                line_items=line_items
            ))
        
        # Validate all orders
        validation_result = self.processor.data_validator.validate_orders(orders)
        assert validation_result.status in [ProcessingStatus.SUCCESS, ProcessingStatus.WARNING]
        assert len(validation_result.orders) == 3
        
        # Generate Excel file
        output_file = self.temp_dir / "batch_test_output.xlsx"
        excel_file = self.processor.excel_generator.generate_excel(
            validation_result,
            str(output_file)
        )
        
        # Verify Excel file
        assert Path(excel_file).exists()
        
        import openpyxl
        wb = openpyxl.load_workbook(excel_file)
        ws = wb.active
        
        # Should have header + 3 data rows
        assert ws.max_row == 4
        
        # Check that all orders are present
        order_numbers = [ws.cell(row, 1).value for row in range(2, 5)]
        expected_orders = ["PO-2024-001", "PO-2024-002", "PO-2024-003"]
        assert set(order_numbers) == set(expected_orders)
        
        wb.close()
    
    def test_config_customization(self):
        """Test processing with custom configuration."""
        custom_config = Config(
            decimal_places=3,
            valid_currencies=["EUR", "GBP"],
            min_quantity=0.001,
            log_level="ERROR"
        )
        
        custom_processor = PDF2ExcelProcessor(custom_config)
        
        # Create order with EUR currency and high precision
        line_items = [
            OrderLineItem(
                material_code="PRECISION-ITEM",
                quantity=Decimal("0.005"),  # Very small quantity
                unit_of_measure="G",
                net_price=Decimal("123.456"),  # High precision price
                currency="EUR"
            )
        ]
        
        orders = [
            OrderData(
                order_number="PO-PRECISION-001",
                customer_code="CUST-EUR",
                delivery_date=datetime(2024, 12, 15),
                line_items=line_items
            )
        ]
        
        # Validate with custom config
        validation_result = custom_processor.data_validator.validate_orders(orders)
        assert validation_result.status in [ProcessingStatus.SUCCESS, ProcessingStatus.WARNING]
        
        # Check that small quantity is accepted
        validated_item = validation_result.orders[0].line_items[0]
        assert validated_item.quantity == Decimal("0.005")
        assert validated_item.currency == "EUR"
    
    def test_error_recovery(self):
        """Test system behavior with mixed valid and invalid data."""
        # Mix of valid and invalid orders
        valid_items = [
            OrderLineItem(
                material_code="VALID-ITEM",
                quantity=Decimal("10.0"),
                unit_of_measure="EA",
                net_price=Decimal("1.00"),
                currency="USD"
            )
        ]
        
        valid_order = OrderData(
            order_number="PO-VALID-001",
            customer_code="CUST-VALID",
            delivery_date=datetime(2024, 12, 15),
            line_items=valid_items
        )
        
        # Create an order that will have validation warnings
        warning_items = [
            OrderLineItem(
                material_code="WARNING-ITEM",
                quantity=Decimal("999999.0"),  # Very large quantity - should generate warning
                unit_of_measure="EA",
                net_price=Decimal("0.01"),
                currency="USD"
            )
        ]
        
        warning_order = OrderData(
            order_number="PO-WARNING-001",
            customer_code="CUST-WARNING",
            delivery_date=datetime(2024, 12, 15),
            line_items=warning_items
        )
        
        orders = [valid_order, warning_order]
        
        # Validate orders
        validation_result = self.processor.data_validator.validate_orders(orders)
        
        # Should have warnings but still process both orders
        assert validation_result.status == ProcessingStatus.WARNING
        assert len(validation_result.orders) == 2
        assert len(validation_result.warnings) > 0
        
        # Generate Excel file
        output_file = self.temp_dir / "mixed_validation_output.xlsx"
        excel_file = self.processor.excel_generator.generate_excel(
            validation_result,
            str(output_file)
        )
        
        assert Path(excel_file).exists()
    
    def test_processing_stats(self):
        """Test processing statistics functionality."""
        stats = self.processor.get_processing_stats()
        
        # Verify stats structure
        assert "version" in stats
        assert "config" in stats
        assert "components" in stats
        
        # Verify config stats
        config_stats = stats["config"]
        required_config_keys = [
            "ocr_language", "valid_currencies", 
            "date_format", "decimal_places"
        ]
        for key in required_config_keys:
            assert key in config_stats
        
        # Verify component stats
        components = stats["components"]
        required_components = [
            "pdf_extractor", "field_mapper", 
            "data_validator", "excel_generator"
        ]
        for component in required_components:
            assert component in components


if __name__ == "__main__":
    # Run tests if executed directly
    pytest.main([__file__, "-v"])
