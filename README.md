# PDF2Excel - Pharmaceutical Order Confirmation Processor

A specialized document transformation system that converts PDF order confirmation files into SAP-ready Excel files for wholesale pharmaceutical operations.

## Features

- **PDF Content Extraction**: Supports both text-based and scanned (OCR) PDFs
- **Intelligent Field Mapping**: Automatically identifies and maps order data to SAP import format
- **Data Validation**: Ensures data quality with comprehensive validation rules
- **SAP-Ready Output**: Generates Excel files formatted exactly for SAP import
- **Error Handling**: Robust processing of multiple orders and missing data scenarios

## SAP Import Format

The system generates Excel files with the following columns:
- **A**: Order Number
- **B**: Customer Code  
- **C**: Delivery Date (YYYY.MM.DD)
- **D**: Material Code
- **E**: Quantity
- **F**: Unit of Measure
- **G**: Net Price
- **H**: Currency
- **I**: Discount (%)

## Installation

1. Install Python 3.8 or higher
2. Install required packages:
   ```bash
   pip install -r requirements.txt
   ```
3. Install Tesseract OCR:
   - Windows: Download from https://github.com/UB-Mannheim/tesseract/wiki
   - Linux: `sudo apt-get install tesseract-ocr`
   - macOS: `brew install tesseract`

## Usage

```python
from pdf2excel import PDF2ExcelProcessor

processor = PDF2ExcelProcessor()
result = processor.process_pdf("order_confirmation.pdf")
print(f"Excel file saved: {result.output_file}")
```

## Project Structure

```
pdf2excel/
├── src/
│   ├── pdf2excel/
│   │   ├── __init__.py
│   │   ├── core/
│   │   │   ├── __init__.py
│   │   │   ├── pdf_extractor.py
│   │   │   ├── field_mapper.py
│   │   │   ├── data_validator.py
│   │   │   └── excel_generator.py
│   │   ├── models/
│   │   │   ├── __init__.py
│   │   │   └── order_data.py
│   │   └── utils/
│   │       ├── __init__.py
│   │       ├── config.py
│   │       └── logger.py
├── tests/
├── examples/
├── requirements.txt
└── README.md
```
