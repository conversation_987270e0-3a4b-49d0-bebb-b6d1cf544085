#!/usr/bin/env python3
"""Command-line interface for PDF2Excel processor."""

import argparse
import sys
from pathlib import Path
from typing import Optional

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

from pdf2excel import PDF2ExcelProcessor
from pdf2excel.utils.config import Config
from pdf2excel.models.order_data import ProcessingStatus


def create_parser() -> argparse.ArgumentParser:
    """Create command-line argument parser."""
    parser = argparse.ArgumentParser(
        description="Convert PDF order confirmations to SAP-ready Excel files",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s input.pdf                          # Process single PDF
  %(prog)s input.pdf -o output.xlsx           # Specify output file
  %(prog)s -d pdf_folder -od output_folder    # Batch process directory
  %(prog)s input.pdf --ocr-lang deu           # Use German OCR
  %(prog)s input.pdf --debug                  # Enable debug logging
        """
    )
    
    # Input options
    input_group = parser.add_mutually_exclusive_group(required=True)
    input_group.add_argument(
        "pdf_file",
        nargs="?",
        help="Input PDF file to process"
    )
    input_group.add_argument(
        "-d", "--directory",
        help="Directory containing PDF files to process"
    )
    
    # Output options
    parser.add_argument(
        "-o", "--output",
        help="Output Excel file path (for single file processing)"
    )
    parser.add_argument(
        "-od", "--output-directory",
        help="Output directory for processed files (for batch processing)"
    )
    
    # OCR options
    parser.add_argument(
        "--ocr-lang",
        default="eng",
        help="OCR language code (default: eng)"
    )
    parser.add_argument(
        "--ocr-dpi",
        type=int,
        default=300,
        help="DPI for PDF to image conversion (default: 300)"
    )
    parser.add_argument(
        "--tesseract-cmd",
        help="Path to tesseract executable"
    )
    
    # Validation options
    parser.add_argument(
        "--min-quantity",
        type=float,
        default=0.01,
        help="Minimum valid quantity (default: 0.01)"
    )
    parser.add_argument(
        "--max-quantity",
        type=float,
        default=999999.99,
        help="Maximum valid quantity (default: 999999.99)"
    )
    parser.add_argument(
        "--currencies",
        nargs="+",
        default=["USD", "EUR", "GBP", "CAD", "AUD"],
        help="Valid currency codes (default: USD EUR GBP CAD AUD)"
    )
    parser.add_argument(
        "--decimal-places",
        type=int,
        default=2,
        help="Decimal places for prices (default: 2)"
    )
    
    # Logging options
    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"],
        default="INFO",
        help="Logging level (default: INFO)"
    )
    parser.add_argument(
        "--log-file",
        help="Log file path (default: pdf2excel.log)"
    )
    parser.add_argument(
        "--debug",
        action="store_true",
        help="Enable debug logging (equivalent to --log-level DEBUG)"
    )
    parser.add_argument(
        "--quiet",
        action="store_true",
        help="Suppress console output except errors"
    )
    
    # Other options
    parser.add_argument(
        "--version",
        action="version",
        version="PDF2Excel 1.0.0"
    )
    
    return parser


def create_config_from_args(args) -> Config:
    """Create configuration from command-line arguments."""
    config_kwargs = {}
    
    # OCR settings
    if args.ocr_lang:
        config_kwargs["ocr_language"] = args.ocr_lang
    if args.ocr_dpi:
        config_kwargs["ocr_dpi"] = args.ocr_dpi
    if args.tesseract_cmd:
        config_kwargs["tesseract_cmd"] = args.tesseract_cmd
    
    # Validation settings
    if args.min_quantity:
        config_kwargs["min_quantity"] = args.min_quantity
    if args.max_quantity:
        config_kwargs["max_quantity"] = args.max_quantity
    if args.currencies:
        config_kwargs["valid_currencies"] = args.currencies
    if args.decimal_places:
        config_kwargs["decimal_places"] = args.decimal_places
    
    # Logging settings
    if args.debug:
        config_kwargs["log_level"] = "DEBUG"
    elif args.quiet:
        config_kwargs["log_level"] = "ERROR"
    elif args.log_level:
        config_kwargs["log_level"] = args.log_level
    
    if args.log_file:
        config_kwargs["log_file"] = args.log_file
    
    return Config(**config_kwargs)


def process_single_file(processor: PDF2ExcelProcessor, pdf_file: str, 
                       output_file: Optional[str], quiet: bool) -> int:
    """Process a single PDF file."""
    if not quiet:
        print(f"Processing: {pdf_file}")
    
    result = processor.process_pdf(pdf_file, output_file)
    
    if not quiet:
        print(f"Status: {result.status.value.upper()}")
        print(f"Orders: {result.total_orders}")
        print(f"Line Items: {result.total_line_items}")
        print(f"Processing Time: {result.processing_time:.2f} seconds")
        
        if result.output_file:
            print(f"Output: {result.output_file}")
        
        if result.warnings:
            print(f"\nWarnings ({len(result.warnings)}):")
            for warning in result.warnings:
                print(f"  - {warning}")
        
        if result.errors:
            print(f"\nErrors ({len(result.errors)}):")
            for error in result.errors:
                print(f"  - {error}")
    
    # Return exit code based on status
    if result.status == ProcessingStatus.ERROR:
        return 1
    elif result.status == ProcessingStatus.WARNING:
        return 2
    else:
        return 0


def process_directory(processor: PDF2ExcelProcessor, pdf_directory: str, 
                     output_directory: str, quiet: bool) -> int:
    """Process multiple PDF files in a directory."""
    if not quiet:
        print(f"Processing directory: {pdf_directory}")
        print(f"Output directory: {output_directory}")
    
    try:
        results = processor.process_multiple_pdfs(pdf_directory, output_directory)
        
        if not quiet:
            print(f"\nProcessed {len(results)} files:")
            
            success_count = 0
            warning_count = 0
            error_count = 0
            
            for filename, result in results.items():
                status_symbol = {
                    ProcessingStatus.SUCCESS: "✓",
                    ProcessingStatus.WARNING: "⚠",
                    ProcessingStatus.ERROR: "✗"
                }[result.status]
                
                print(f"  {status_symbol} {filename}: {result.total_orders} orders, {result.total_line_items} items")
                
                if result.status == ProcessingStatus.SUCCESS:
                    success_count += 1
                elif result.status == ProcessingStatus.WARNING:
                    warning_count += 1
                else:
                    error_count += 1
            
            print(f"\nSummary: {success_count} successful, {warning_count} with warnings, {error_count} failed")
        
        # Return exit code based on results
        if any(r.status == ProcessingStatus.ERROR for r in results.values()):
            return 1
        elif any(r.status == ProcessingStatus.WARNING for r in results.values()):
            return 2
        else:
            return 0
            
    except Exception as e:
        print(f"Error processing directory: {e}", file=sys.stderr)
        return 1


def main() -> int:
    """Main CLI entry point."""
    parser = create_parser()
    args = parser.parse_args()
    
    try:
        # Create configuration from arguments
        config = create_config_from_args(args)
        
        # Initialize processor
        processor = PDF2ExcelProcessor(config)
        
        # Process based on input type
        if args.pdf_file:
            # Single file processing
            return process_single_file(processor, args.pdf_file, args.output, args.quiet)
        
        elif args.directory:
            # Directory processing
            output_dir = args.output_directory or "output"
            return process_directory(processor, args.directory, output_dir, args.quiet)
        
        else:
            parser.print_help()
            return 1
            
    except KeyboardInterrupt:
        print("\nProcessing interrupted by user", file=sys.stderr)
        return 130
    except Exception as e:
        print(f"Unexpected error: {e}", file=sys.stderr)
        return 1


if __name__ == "__main__":
    sys.exit(main())
