"""Excel file generation for SAP import."""

from pathlib import Path
from typing import List, Dict, Any, Optional
from datetime import datetime
from decimal import Decimal

import pandas as pd
from openpyxl import Workbook
from openpyxl.styles import Font, Alignment, Border, Side, PatternFill
from openpyxl.utils.dataframe import dataframe_to_rows

from ..models.order_data import OrderData, ProcessingResult
from ..utils.logger import get_logger
from ..utils.config import Config

logger = get_logger(__name__)


class ExcelGenerator:
    """Generate SAP-ready Excel files from order data."""
    
    def __init__(self, config: Config):
        """Initialize Excel generator with configuration."""
        self.config = config
        
        # SAP import column mapping
        self.sap_columns = {
            'A': 'Order Number',
            'B': 'Customer Code',
            'C': 'Delivery Date',
            'D': 'Material Code',
            'E': 'Quantity',
            'F': 'Unit of Measure',
            'G': 'Net Price',
            'H': 'Currency',
            'I': 'Discount (%)'
        }
        
        # Additional columns for remarks and metadata
        self.additional_columns = {
            'J': 'Remarks',
            'K': 'Line Total',
            'L': 'Order Date',
            'M': 'Customer Name'
        }
    
    def generate_excel(self, processing_result: ProcessingResult, output_path: str) -> str:
        """
        Generate Excel file from processing result.
        
        Args:
            processing_result: Processing result with validated orders
            output_path: Path for output Excel file
            
        Returns:
            Path to generated Excel file
        """
        logger.info(f"Generating Excel file: {output_path}")
        
        if not processing_result.orders:
            raise ValueError("No orders to export")
        
        # Convert orders to DataFrame
        df = self._orders_to_dataframe(processing_result.orders)
        
        # Generate Excel file
        output_file = self._create_excel_file(df, output_path, processing_result)
        
        logger.info(f"Excel file generated successfully: {output_file}")
        return output_file
    
    def _orders_to_dataframe(self, orders: List[OrderData]) -> pd.DataFrame:
        """Convert orders to pandas DataFrame in SAP format."""
        rows = []
        
        for order in orders:
            for line_item in order.line_items:
                row = {
                    'Order Number': order.order_number,
                    'Customer Code': order.customer_code,
                    'Delivery Date': self._format_date_for_sap(order.delivery_date),
                    'Material Code': line_item.material_code,
                    'Quantity': float(line_item.quantity),
                    'Unit of Measure': line_item.unit_of_measure,
                    'Net Price': float(line_item.net_price),
                    'Currency': line_item.currency,
                    'Discount (%)': float(line_item.discount_percent) if line_item.discount_percent else 0.0,
                    'Remarks': line_item.remarks or '',
                    'Line Total': float(line_item.line_total) if line_item.line_total else float(line_item.quantity * line_item.net_price),
                    'Order Date': self._format_date_for_sap(order.order_date) if order.order_date else '',
                    'Customer Name': order.customer_name or ''
                }
                rows.append(row)
        
        df = pd.DataFrame(rows)
        
        # Ensure proper column order
        column_order = list(self.sap_columns.values()) + list(self.additional_columns.values())
        df = df.reindex(columns=column_order)
        
        logger.info(f"Created DataFrame with {len(df)} rows and {len(df.columns)} columns")
        return df
    
    def _format_date_for_sap(self, date_value: Optional[datetime]) -> str:
        """Format date for SAP import (YYYY.MM.DD)."""
        if not date_value:
            return ''
        
        if isinstance(date_value, datetime):
            return date_value.strftime('%Y.%m.%d')
        else:
            return date_value.strftime('%Y.%m.%d')
    
    def _create_excel_file(self, df: pd.DataFrame, output_path: str, 
                          processing_result: ProcessingResult) -> str:
        """Create formatted Excel file."""
        output_path = Path(output_path)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Create workbook
        wb = Workbook()
        ws = wb.active
        ws.title = self.config.excel_sheet_name
        
        # Add data to worksheet
        for r in dataframe_to_rows(df, index=False, header=True):
            ws.append(r)
        
        # Apply formatting
        self._format_worksheet(ws, df)
        
        # Add metadata sheet
        self._add_metadata_sheet(wb, processing_result)
        
        # Save file
        wb.save(output_path)
        
        return str(output_path)
    
    def _format_worksheet(self, ws, df: pd.DataFrame) -> None:
        """Apply formatting to the worksheet."""
        # Header formatting
        header_font = Font(bold=True, color="FFFFFF")
        header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        header_alignment = Alignment(horizontal="center", vertical="center")
        
        # Border style
        thin_border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
        
        # Format header row
        for col_num, cell in enumerate(ws[1], 1):
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = header_alignment
            cell.border = thin_border
        
        # Format data rows
        for row_num in range(2, len(df) + 2):
            for col_num in range(1, len(df.columns) + 1):
                cell = ws.cell(row=row_num, column=col_num)
                cell.border = thin_border
                
                # Special formatting for specific columns
                column_name = df.columns[col_num - 1]
                
                if 'Date' in column_name:
                    cell.alignment = Alignment(horizontal="center")
                elif column_name in ['Quantity', 'Net Price', 'Discount (%)', 'Line Total']:
                    cell.alignment = Alignment(horizontal="right")
                    if column_name in ['Net Price', 'Line Total']:
                        cell.number_format = f'#,##0.{self.config.decimal_places * "0"}'
                    elif column_name == 'Quantity':
                        cell.number_format = '#,##0.000'
                    elif column_name == 'Discount (%)':
                        cell.number_format = '0.00%'
                else:
                    cell.alignment = Alignment(horizontal="left")
        
        # Auto-adjust column widths
        self._auto_adjust_columns(ws, df)
        
        # Freeze header row
        ws.freeze_panes = 'A2'
    
    def _auto_adjust_columns(self, ws, df: pd.DataFrame) -> None:
        """Auto-adjust column widths based on content."""
        for col_num, column_name in enumerate(df.columns, 1):
            column_letter = ws.cell(row=1, column=col_num).column_letter
            
            # Calculate max width needed
            max_length = len(str(column_name))  # Header length
            
            for row_num in range(2, len(df) + 2):
                cell_value = ws.cell(row=row_num, column=col_num).value
                if cell_value:
                    max_length = max(max_length, len(str(cell_value)))
            
            # Set column width with some padding
            adjusted_width = min(max_length + 2, 50)  # Cap at 50 characters
            ws.column_dimensions[column_letter].width = adjusted_width
    
    def _add_metadata_sheet(self, wb: Workbook, processing_result: ProcessingResult) -> None:
        """Add metadata sheet with processing information."""
        ws_meta = wb.create_sheet("Processing_Info")
        
        # Add processing metadata
        metadata = [
            ["Processing Information", ""],
            ["Input File", processing_result.input_file],
            ["Processing Time (seconds)", f"{processing_result.processing_time:.2f}"],
            ["Pages Processed", processing_result.pages_processed],
            ["OCR Used", "Yes" if processing_result.ocr_used else "No"],
            ["Total Orders", processing_result.total_orders],
            ["Total Line Items", processing_result.total_line_items],
            ["Processing Status", processing_result.status.value],
            ["Generated On", datetime.now().strftime("%Y-%m-%d %H:%M:%S")],
            ["", ""],
            ["Warnings", ""],
        ]
        
        # Add warnings
        for warning in processing_result.warnings:
            metadata.append(["", warning])
        
        metadata.append(["", ""])
        metadata.append(["Errors", ""])
        
        # Add errors
        for error in processing_result.errors:
            metadata.append(["", error])
        
        # Add data to sheet
        for row in metadata:
            ws_meta.append(row)
        
        # Format metadata sheet
        header_font = Font(bold=True)
        for row_num in range(1, len(metadata) + 1):
            cell_a = ws_meta.cell(row=row_num, column=1)
            if cell_a.value in ["Processing Information", "Warnings", "Errors"]:
                cell_a.font = header_font
        
        # Auto-adjust columns
        for col in ['A', 'B']:
            max_length = 0
            for row_num in range(1, len(metadata) + 1):
                cell_value = ws_meta[f'{col}{row_num}'].value
                if cell_value:
                    max_length = max(max_length, len(str(cell_value)))
            ws_meta.column_dimensions[col].width = min(max_length + 2, 80)
    
    def generate_summary_report(self, processing_result: ProcessingResult, output_dir: str) -> str:
        """Generate a summary report of the processing."""
        output_path = Path(output_dir) / f"processing_summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write("PDF2Excel Processing Summary\n")
            f.write("=" * 50 + "\n\n")
            
            f.write(f"Input File: {processing_result.input_file}\n")
            f.write(f"Processing Time: {processing_result.processing_time:.2f} seconds\n")
            f.write(f"Pages Processed: {processing_result.pages_processed}\n")
            f.write(f"OCR Used: {'Yes' if processing_result.ocr_used else 'No'}\n")
            f.write(f"Status: {processing_result.status.value.upper()}\n\n")
            
            f.write(f"Orders Processed: {processing_result.total_orders}\n")
            f.write(f"Line Items: {processing_result.total_line_items}\n\n")
            
            if processing_result.warnings:
                f.write("Warnings:\n")
                for warning in processing_result.warnings:
                    f.write(f"  - {warning}\n")
                f.write("\n")
            
            if processing_result.errors:
                f.write("Errors:\n")
                for error in processing_result.errors:
                    f.write(f"  - {error}\n")
                f.write("\n")
            
            f.write(f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        
        logger.info(f"Summary report generated: {output_path}")
        return str(output_path)
