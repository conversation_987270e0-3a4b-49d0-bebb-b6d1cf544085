# Makefile for PDF2Excel project

.PHONY: help install install-dev test test-coverage lint format clean build docs run-example

# Default target
help:
	@echo "PDF2Excel - Pharmaceutical Order Processing System"
	@echo ""
	@echo "Available targets:"
	@echo "  install      - Install package and dependencies"
	@echo "  install-dev  - Install package with development dependencies"
	@echo "  test         - Run tests"
	@echo "  test-coverage - Run tests with coverage report"
	@echo "  lint         - Run code linting"
	@echo "  format       - Format code with black"
	@echo "  clean        - Clean build artifacts"
	@echo "  build        - Build package"
	@echo "  docs         - Build documentation"
	@echo "  run-example  - Run basic usage example"

# Installation targets
install:
	pip install -r requirements.txt
	pip install -e .

install-dev:
	pip install -r requirements.txt
	pip install -e ".[dev]"

# Testing targets
test:
	python -m pytest tests/ -v

test-coverage:
	python -m pytest tests/ -v --cov=src/pdf2excel --cov-report=html --cov-report=term

# Code quality targets
lint:
	flake8 src/ tests/ examples/
	mypy src/pdf2excel

format:
	black src/ tests/ examples/
	isort src/ tests/ examples/

# Build targets
clean:
	rm -rf build/
	rm -rf dist/
	rm -rf *.egg-info/
	rm -rf .pytest_cache/
	rm -rf htmlcov/
	find . -type d -name __pycache__ -exec rm -rf {} +
	find . -type f -name "*.pyc" -delete

build: clean
	python setup.py sdist bdist_wheel

# Documentation
docs:
	@echo "Documentation build not implemented yet"
	@echo "Consider adding Sphinx documentation"

# Example execution
run-example:
	python examples/basic_usage.py

# Development helpers
setup-dev: install-dev
	@echo "Setting up development environment..."
	@echo "Installing pre-commit hooks..."
	@pip install pre-commit
	@echo "Development setup complete!"

# Testing with different Python versions (requires tox)
test-all:
	@echo "Multi-version testing not configured yet"
	@echo "Consider adding tox configuration"

# Package validation
validate-package: build
	twine check dist/*

# Installation verification
verify-install:
	python -c "import pdf2excel; print('PDF2Excel imported successfully')"
	python -c "from pdf2excel import PDF2ExcelProcessor; print('Main processor available')"

# Quick development cycle
dev-cycle: format lint test
	@echo "Development cycle complete: format -> lint -> test"

# Production readiness check
prod-check: clean format lint test-coverage build validate-package
	@echo "Production readiness check complete"
