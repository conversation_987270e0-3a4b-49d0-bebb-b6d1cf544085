"""Pytest configuration and fixtures."""

import pytest
import tempfile
import shutil
from pathlib import Path
from datetime import datetime
from decimal import Decimal

import sys
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from pdf2excel import PDF2ExcelProcessor
from pdf2excel.utils.config import Config
from pdf2excel.models.order_data import OrderData, OrderLineItem


@pytest.fixture
def temp_dir():
    """Create a temporary directory for tests."""
    temp_path = Path(tempfile.mkdtemp())
    yield temp_path
    if temp_path.exists():
        shutil.rmtree(temp_path)


@pytest.fixture
def test_config():
    """Create a test configuration with suppressed logging."""
    return Config(log_level="ERROR")


@pytest.fixture
def processor(test_config):
    """Create a PDF2Excel processor for testing."""
    return PDF2ExcelProcessor(test_config)


@pytest.fixture
def sample_line_item():
    """Create a sample order line item."""
    return OrderLineItem(
        material_code="TEST-ITEM-001",
        quantity=Decimal("10.000"),
        unit_of_measure="EA",
        net_price=Decimal("15.99"),
        currency="USD",
        discount_percent=Decimal("5.00")
    )


@pytest.fixture
def sample_order(sample_line_item):
    """Create a sample order with line items."""
    return OrderData(
        order_number="PO-TEST-001",
        customer_code="CUST-TEST",
        delivery_date=datetime(2024, 12, 15),
        line_items=[sample_line_item],
        customer_name="Test Customer"
    )


@pytest.fixture
def multiple_sample_orders():
    """Create multiple sample orders for batch testing."""
    orders = []
    
    for i in range(3):
        line_items = [
            OrderLineItem(
                material_code=f"ITEM-{i+1:03d}",
                quantity=Decimal(f"{(i+1)*5}.000"),
                unit_of_measure="EA",
                net_price=Decimal(f"{(i+1)*2.5:.2f}"),
                currency="USD"
            )
        ]
        
        orders.append(OrderData(
            order_number=f"PO-BATCH-{i+1:03d}",
            customer_code=f"CUST-{i+1:03d}",
            delivery_date=datetime(2024, 12, 15 + i),
            line_items=line_items
        ))
    
    return orders


@pytest.fixture
def pharmaceutical_sample_orders():
    """Create realistic pharmaceutical order samples."""
    orders = []
    
    # Order 1: Aspirin and Ibuprofen
    order1_items = [
        OrderLineItem(
            material_code="ASPIRIN-100MG",
            quantity=Decimal("500.000"),
            unit_of_measure="EA",
            net_price=Decimal("0.15"),
            currency="USD",
            discount_percent=Decimal("5.00")
        ),
        OrderLineItem(
            material_code="IBUPROFEN-200MG",
            quantity=Decimal("250.000"),
            unit_of_measure="EA",
            net_price=Decimal("0.25"),
            currency="USD",
            discount_percent=Decimal("2.50")
        )
    ]
    
    orders.append(OrderData(
        order_number="PO-PHARMA-001",
        customer_code="PHARMACY-ABC",
        delivery_date=datetime(2024, 12, 20),
        line_items=order1_items,
        customer_name="ABC Pharmacy Chain"
    ))
    
    # Order 2: Liquid medications
    order2_items = [
        OrderLineItem(
            material_code="COUGH-SYRUP-100ML",
            quantity=Decimal("48.000"),
            unit_of_measure="EA",
            net_price=Decimal("8.50"),
            currency="USD",
            discount_percent=Decimal("10.00")
        ),
        OrderLineItem(
            material_code="ANTACID-LIQUID-200ML",
            quantity=Decimal("24.000"),
            unit_of_measure="EA",
            net_price=Decimal("12.75"),
            currency="USD"
        )
    ]
    
    orders.append(OrderData(
        order_number="PO-PHARMA-002",
        customer_code="HOSPITAL-XYZ",
        delivery_date=datetime(2024, 12, 22),
        line_items=order2_items,
        customer_name="XYZ General Hospital"
    ))
    
    return orders
