"""Basic usage example for PDF2Excel processor."""

import sys
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from pdf2excel import PDF2ExcelProcessor
from pdf2excel.utils.config import Config


def main():
    """Demonstrate basic PDF processing."""
    
    # Initialize processor with default configuration
    processor = PDF2ExcelProcessor()
    
    # Example 1: Process a single PDF file
    print("Example 1: Processing single PDF file")
    print("-" * 40)
    
    pdf_file = "sample_order.pdf"  # Replace with actual PDF file path
    
    if Path(pdf_file).exists():
        result = processor.process_pdf(pdf_file)
        
        print(f"Status: {result.status.value}")
        print(f"Orders processed: {result.total_orders}")
        print(f"Line items: {result.total_line_items}")
        print(f"Processing time: {result.processing_time:.2f} seconds")
        
        if result.output_file:
            print(f"Excel file generated: {result.output_file}")
        
        if result.warnings:
            print(f"\nWarnings ({len(result.warnings)}):")
            for warning in result.warnings:
                print(f"  - {warning}")
        
        if result.errors:
            print(f"\nErrors ({len(result.errors)}):")
            for error in result.errors:
                print(f"  - {error}")
    else:
        print(f"PDF file not found: {pdf_file}")
        print("Please place a sample PDF file in the examples directory")
    
    print("\n" + "=" * 50 + "\n")
    
    # Example 2: Process with custom configuration
    print("Example 2: Processing with custom configuration")
    print("-" * 40)
    
    # Create custom configuration
    custom_config = Config(
        ocr_language="eng",
        decimal_places=3,
        valid_currencies=["USD", "EUR", "GBP", "CAD"],
        log_level="DEBUG"
    )
    
    # Initialize processor with custom config
    custom_processor = PDF2ExcelProcessor(custom_config)
    
    # Get processing stats
    stats = custom_processor.get_processing_stats()
    print("Processor Configuration:")
    for key, value in stats["config"].items():
        print(f"  {key}: {value}")
    
    print("\n" + "=" * 50 + "\n")
    
    # Example 3: Batch processing (if directory exists)
    print("Example 3: Batch processing")
    print("-" * 40)
    
    pdf_directory = "sample_pdfs"
    output_directory = "output"
    
    if Path(pdf_directory).exists():
        results = processor.process_multiple_pdfs(pdf_directory, output_directory)
        
        print(f"Processed {len(results)} files:")
        for filename, result in results.items():
            status_symbol = {
                "success": "✓",
                "warning": "⚠",
                "error": "✗"
            }.get(result.status.value, "?")
            
            print(f"  {status_symbol} {filename}: {result.total_orders} orders, {result.total_line_items} items")
    else:
        print(f"PDF directory not found: {pdf_directory}")
        print("Create a 'sample_pdfs' directory with PDF files for batch processing")


if __name__ == "__main__":
    main()
