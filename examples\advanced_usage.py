"""Advanced usage examples for PDF2Excel processor."""

import sys
from pathlib import Path
from decimal import Decimal

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from pdf2excel import PDF2ExcelProcessor
from pdf2excel.utils.config import Config
from pdf2excel.models.order_data import OrderData, OrderLineItem


def custom_field_patterns_example():
    """Example of using custom field patterns for specific document formats."""
    
    print("Advanced Example 1: Custom Field Patterns")
    print("-" * 45)
    
    # Define custom patterns for your specific PDF format
    custom_patterns = {
        "order_number": [
            r"Purchase\s*Order\s*#[\s:]*([A-Z0-9\-]+)",
            r"PO\s*Number[\s:]*([A-Z0-9\-]+)",
            r"Order\s*Reference[\s:]*([A-Z0-9\-]+)"
        ],
        "customer_code": [
            r"Customer\s*ID[\s:]*([A-Z0-9\-]+)",
            r"Account\s*#[\s:]*([A-Z0-9\-]+)",
            r"Client\s*Code[\s:]*([A-Z0-9\-]+)"
        ],
        "delivery_date": [
            r"Ship\s*By[\s:]*(\d{1,2}[\/\-\.]\d{1,2}[\/\-\.]\d{2,4})",
            r"Required\s*Date[\s:]*(\d{1,2}[\/\-\.]\d{1,2}[\/\-\.]\d{2,4})",
            r"Delivery\s*Date[\s:]*(\d{1,2}[\/\-\.]\d{1,2}[\/\-\.]\d{2,4})"
        ]
    }
    
    # Create configuration with custom patterns
    config = Config(
        field_patterns=custom_patterns,
        ocr_language="eng",
        log_level="INFO"
    )
    
    processor = PDF2ExcelProcessor(config)
    
    print("Custom field patterns configured:")
    for field, patterns in custom_patterns.items():
        print(f"  {field}: {len(patterns)} patterns")
    
    return processor


def ocr_configuration_example():
    """Example of OCR-specific configuration."""
    
    print("\nAdvanced Example 2: OCR Configuration")
    print("-" * 45)
    
    # Configure for high-quality OCR processing
    ocr_config = Config(
        tesseract_cmd="tesseract",  # Path to tesseract executable
        ocr_language="eng+deu",     # Multiple languages (English + German)
        ocr_dpi=400,                # Higher DPI for better OCR accuracy
        extract_images=True,
        log_level="DEBUG"
    )
    
    processor = PDF2ExcelProcessor(ocr_config)
    
    print("OCR Configuration:")
    print(f"  Language: {ocr_config.ocr_language}")
    print(f"  DPI: {ocr_config.ocr_dpi}")
    print(f"  Extract Images: {ocr_config.extract_images}")
    
    return processor


def validation_rules_example():
    """Example of custom validation rules."""
    
    print("\nAdvanced Example 3: Custom Validation Rules")
    print("-" * 45)
    
    # Configure strict validation rules
    strict_config = Config(
        min_quantity=0.001,         # Allow very small quantities
        max_quantity=10000.0,       # Limit maximum quantity
        valid_currencies=["USD", "EUR", "GBP"],  # Restrict currencies
        decimal_places=3,           # 3 decimal places for prices
        log_level="INFO"
    )
    
    processor = PDF2ExcelProcessor(strict_config)
    
    print("Validation Rules:")
    print(f"  Quantity range: {strict_config.min_quantity} - {strict_config.max_quantity}")
    print(f"  Valid currencies: {strict_config.valid_currencies}")
    print(f"  Decimal places: {strict_config.decimal_places}")
    
    return processor


def error_handling_example():
    """Example of comprehensive error handling."""
    
    print("\nAdvanced Example 4: Error Handling")
    print("-" * 45)
    
    processor = PDF2ExcelProcessor()
    
    # Test with non-existent file
    result = processor.process_pdf("non_existent_file.pdf")
    
    print(f"Processing non-existent file:")
    print(f"  Status: {result.status.value}")
    print(f"  Errors: {len(result.errors)}")
    
    if result.errors:
        for error in result.errors:
            print(f"    - {error}")
    
    # Test with empty directory
    try:
        results = processor.process_multiple_pdfs("empty_directory", "output")
        print(f"\nBatch processing empty directory: {len(results)} files processed")
    except FileNotFoundError as e:
        print(f"\nExpected error for empty directory: {e}")


def programmatic_data_creation_example():
    """Example of creating order data programmatically for testing."""
    
    print("\nAdvanced Example 5: Programmatic Data Creation")
    print("-" * 45)
    
    from datetime import datetime
    
    # Create sample line items
    line_items = [
        OrderLineItem(
            material_code="ASPIRIN-100MG",
            quantity=Decimal("500.000"),
            unit_of_measure="EA",
            net_price=Decimal("0.15"),
            currency="USD",
            discount_percent=Decimal("5.00")
        ),
        OrderLineItem(
            material_code="IBUPROFEN-200MG",
            quantity=Decimal("250.000"),
            unit_of_measure="EA",
            net_price=Decimal("0.25"),
            currency="USD",
            discount_percent=Decimal("2.50")
        )
    ]
    
    # Create sample order
    order = OrderData(
        order_number="PO-2024-001",
        customer_code="CUST-12345",
        delivery_date=datetime(2024, 12, 15),
        line_items=line_items,
        customer_name="ABC Pharmacy Chain"
    )
    
    print("Created sample order:")
    print(f"  Order Number: {order.order_number}")
    print(f"  Customer: {order.customer_code}")
    print(f"  Line Items: {len(order.line_items)}")
    
    # Validate the order
    processor = PDF2ExcelProcessor()
    validation_result = processor.data_validator.validate_orders([order])
    
    print(f"  Validation Status: {validation_result.status.value}")
    print(f"  Warnings: {len(validation_result.warnings)}")
    print(f"  Errors: {len(validation_result.errors)}")
    
    # Generate Excel from programmatic data
    if validation_result.orders:
        excel_file = processor.excel_generator.generate_excel(
            validation_result, 
            "programmatic_order.xlsx"
        )
        print(f"  Excel generated: {excel_file}")


def main():
    """Run all advanced examples."""
    
    print("PDF2Excel Advanced Usage Examples")
    print("=" * 50)
    
    # Run examples
    custom_field_patterns_example()
    ocr_configuration_example()
    validation_rules_example()
    error_handling_example()
    programmatic_data_creation_example()
    
    print("\n" + "=" * 50)
    print("Advanced examples completed!")
    print("\nFor production use:")
    print("1. Customize field patterns for your specific PDF formats")
    print("2. Configure OCR settings based on document quality")
    print("3. Set appropriate validation rules for your business requirements")
    print("4. Implement proper error handling and logging")
    print("5. Test with representative sample documents")


if __name__ == "__main__":
    main()
