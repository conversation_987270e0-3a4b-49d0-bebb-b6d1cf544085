"""Field identification and mapping for pharmaceutical orders."""

import re
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
from decimal import Decimal, InvalidOperation
from dateutil import parser as date_parser
from fuzzywuzzy import fuzz, process

from ..models.order_data import OrderData, OrderLineItem, ExtractionContext
from ..utils.logger import get_logger
from ..utils.config import Config

logger = get_logger(__name__)


class FieldMapper:
    """Map extracted PDF content to structured order data."""
    
    def __init__(self, config: Config):
        """Initialize field mapper with configuration."""
        self.config = config
        self.field_patterns = config.field_patterns
        
        # Common pharmaceutical terms for better matching
        self.pharma_terms = {
            'quantity': ['qty', 'quantity', 'amount', 'units', 'count', 'pieces'],
            'price': ['price', 'cost', 'rate', 'unit price', 'net price', 'amount'],
            'currency': ['currency', 'curr', 'ccy'],
            'discount': ['discount', 'disc', 'rebate', 'reduction'],
            'unit': ['unit', 'uom', 'unit of measure', 'measure', 'each', 'ea', 'kg', 'g', 'l', 'ml']
        }
    
    def map_fields(self, contexts: List[ExtractionContext]) -> List[OrderData]:
        """
        Map extraction contexts to structured order data.
        
        Args:
            contexts: List of extraction contexts from PDF pages
            
        Returns:
            List of structured order data
        """
        logger.info(f"Mapping fields from {len(contexts)} contexts")
        orders = []
        
        # First, try to identify order-level information
        order_info = self._extract_order_info(contexts)
        
        # Then extract line items from tables
        line_items = self._extract_line_items(contexts)
        
        if line_items:
            # Group line items by order (if multiple orders in one PDF)
            grouped_items = self._group_line_items(line_items, order_info)
            
            for order_key, items in grouped_items.items():
                try:
                    order = self._create_order_data(order_info, items, order_key)
                    orders.append(order)
                except Exception as e:
                    logger.error(f"Failed to create order {order_key}: {e}")
        
        logger.info(f"Mapped {len(orders)} orders with {sum(len(o.line_items) for o in orders)} line items")
        return orders
    
    def _extract_order_info(self, contexts: List[ExtractionContext]) -> Dict[str, Any]:
        """Extract order-level information from contexts."""
        order_info = {}
        
        # Combine all text for order-level field extraction
        combined_text = "\n".join(ctx.raw_text for ctx in contexts)
        
        # Extract order number
        order_number = self._extract_field_with_patterns(
            combined_text, 
            self.field_patterns.get('order_number', [])
        )
        if order_number:
            order_info['order_number'] = order_number
        
        # Extract customer code
        customer_code = self._extract_field_with_patterns(
            combined_text,
            self.field_patterns.get('customer_code', [])
        )
        if customer_code:
            order_info['customer_code'] = customer_code
        
        # Extract delivery date
        delivery_date = self._extract_date_field(
            combined_text,
            self.field_patterns.get('delivery_date', [])
        )
        if delivery_date:
            order_info['delivery_date'] = delivery_date
        
        # Extract additional info
        order_info.update(self._extract_additional_info(combined_text))
        
        return order_info
    
    def _extract_line_items(self, contexts: List[ExtractionContext]) -> List[Dict[str, Any]]:
        """Extract line items from table data."""
        line_items = []
        
        for context in contexts:
            for table in context.tables:
                items = self._process_table_for_line_items(table, context)
                line_items.extend(items)
        
        return line_items
    
    def _process_table_for_line_items(self, table: Dict[str, Any], context: ExtractionContext) -> List[Dict[str, Any]]:
        """Process a single table to extract line items."""
        items = []
        
        if not table.get('data'):
            return items
        
        headers = table.get('headers', [])
        data_rows = table.get('data', [])
        
        # Map headers to standard field names
        header_mapping = self._map_table_headers(headers)
        
        for row_idx, row in enumerate(data_rows):
            try:
                item = self._extract_line_item_from_row(row, header_mapping, context, row_idx)
                if item and self._is_valid_line_item(item):
                    items.append(item)
            except Exception as e:
                logger.warning(f"Failed to process row {row_idx} in table: {e}")
        
        return items
    
    def _map_table_headers(self, headers: List[str]) -> Dict[str, str]:
        """Map table headers to standard field names using fuzzy matching."""
        mapping = {}
        
        standard_fields = {
            'material_code': ['item', 'product', 'material', 'code', 'sku', 'part', 'number', 'id'],
            'quantity': self.pharma_terms['quantity'],
            'unit_of_measure': self.pharma_terms['unit'],
            'net_price': self.pharma_terms['price'],
            'currency': self.pharma_terms['currency'],
            'discount_percent': self.pharma_terms['discount']
        }
        
        for header in headers:
            if not header or not isinstance(header, str):
                continue
                
            header_clean = header.lower().strip()
            best_match = None
            best_score = 0
            
            for field_name, field_terms in standard_fields.items():
                for term in field_terms:
                    score = fuzz.ratio(header_clean, term.lower())
                    if score > best_score and score > 70:  # Minimum similarity threshold
                        best_score = score
                        best_match = field_name
            
            if best_match:
                mapping[header] = best_match
                logger.debug(f"Mapped header '{header}' to '{best_match}' (score: {best_score})")
        
        return mapping
    
    def _extract_line_item_from_row(self, row: Dict[str, Any], header_mapping: Dict[str, str], 
                                   context: ExtractionContext, row_idx: int) -> Optional[Dict[str, Any]]:
        """Extract line item data from a table row."""
        item = {}
        
        for header, value in row.items():
            if not header or header not in header_mapping:
                continue
            
            field_name = header_mapping[header]
            processed_value = self._process_field_value(field_name, value)
            
            if processed_value is not None:
                item[field_name] = processed_value
        
        # Add metadata
        item['_source'] = {
            'page': context.page_number,
            'row_index': row_idx,
            'extraction_method': context.extraction_method
        }
        
        return item if item else None
    
    def _process_field_value(self, field_name: str, value: Any) -> Any:
        """Process and validate field values based on field type."""
        if value is None or (isinstance(value, str) and not value.strip()):
            return None
        
        value_str = str(value).strip()
        
        try:
            if field_name == 'material_code':
                # Clean material code
                return re.sub(r'[^\w\-]', '', value_str).upper()
            
            elif field_name == 'quantity':
                # Parse quantity
                quantity_match = re.search(r'[\d,]+\.?\d*', value_str.replace(',', ''))
                if quantity_match:
                    return Decimal(quantity_match.group())
                return None
            
            elif field_name == 'net_price':
                # Parse price
                price_match = re.search(r'[\d,]+\.?\d*', value_str.replace(',', ''))
                if price_match:
                    return Decimal(price_match.group())
                return None
            
            elif field_name == 'discount_percent':
                # Parse discount percentage
                discount_match = re.search(r'(\d+\.?\d*)%?', value_str)
                if discount_match:
                    return Decimal(discount_match.group(1))
                return None
            
            elif field_name == 'currency':
                # Extract currency code
                currency_match = re.search(r'[A-Z]{3}', value_str.upper())
                if currency_match:
                    return currency_match.group()
                return 'USD'  # Default currency
            
            elif field_name == 'unit_of_measure':
                # Standardize unit of measure
                unit_clean = value_str.upper()
                unit_mapping = {
                    'EACH': 'EA', 'PIECE': 'EA', 'PIECES': 'EA',
                    'KILOGRAM': 'KG', 'KILOGRAMS': 'KG',
                    'GRAM': 'G', 'GRAMS': 'G',
                    'LITER': 'L', 'LITERS': 'L', 'LITRE': 'L', 'LITRES': 'L',
                    'MILLILITER': 'ML', 'MILLILITERS': 'ML', 'MILLILITRE': 'ML'
                }
                return unit_mapping.get(unit_clean, unit_clean)
            
            else:
                return value_str
                
        except (ValueError, InvalidOperation) as e:
            logger.warning(f"Failed to process {field_name} value '{value}': {e}")
            return None
    
    def _is_valid_line_item(self, item: Dict[str, Any]) -> bool:
        """Check if line item has minimum required fields."""
        required_fields = ['material_code', 'quantity']
        return all(field in item and item[field] is not None for field in required_fields)
    
    def _extract_field_with_patterns(self, text: str, patterns: List[str]) -> Optional[str]:
        """Extract field value using regex patterns."""
        for pattern in patterns:
            match = re.search(pattern, text, re.IGNORECASE | re.MULTILINE)
            if match:
                return match.group(1).strip()
        return None
    
    def _extract_date_field(self, text: str, patterns: List[str]) -> Optional[datetime]:
        """Extract and parse date field."""
        date_str = self._extract_field_with_patterns(text, patterns)
        if not date_str:
            return None
        
        try:
            return date_parser.parse(date_str)
        except Exception as e:
            logger.warning(f"Failed to parse date '{date_str}': {e}")
            return None
    
    def _extract_additional_info(self, text: str) -> Dict[str, Any]:
        """Extract additional order information."""
        info = {}
        
        # Extract customer name
        customer_patterns = [
            r'customer\s*name[\s:]*([^\n\r]+)',
            r'bill\s*to[\s:]*([^\n\r]+)',
            r'sold\s*to[\s:]*([^\n\r]+)'
        ]
        customer_name = self._extract_field_with_patterns(text, customer_patterns)
        if customer_name:
            info['customer_name'] = customer_name
        
        # Extract order date
        order_date_patterns = [
            r'order\s*date[\s:]*(\d{1,2}[\/\-\.]\d{1,2}[\/\-\.]\d{2,4})',
            r'date[\s:]*(\d{1,2}[\/\-\.]\d{1,2}[\/\-\.]\d{2,4})'
        ]
        order_date = self._extract_date_field(text, order_date_patterns)
        if order_date:
            info['order_date'] = order_date
        
        return info
    
    def _group_line_items(self, line_items: List[Dict[str, Any]], 
                         order_info: Dict[str, Any]) -> Dict[str, List[Dict[str, Any]]]:
        """Group line items by order (handles multiple orders in one PDF)."""
        # For now, assume all items belong to one order
        # This can be enhanced to detect multiple orders based on order numbers in line items
        return {'default': line_items}
    
    def _create_order_data(self, order_info: Dict[str, Any], 
                          line_items: List[Dict[str, Any]], order_key: str) -> OrderData:
        """Create OrderData object from extracted information."""
        
        # Create line item objects
        order_line_items = []
        for item_data in line_items:
            try:
                # Remove metadata before creating OrderLineItem
                clean_data = {k: v for k, v in item_data.items() if not k.startswith('_')}
                
                # Set defaults for missing fields
                if 'unit_of_measure' not in clean_data:
                    clean_data['unit_of_measure'] = 'EA'
                if 'currency' not in clean_data:
                    clean_data['currency'] = 'USD'
                
                line_item = OrderLineItem(**clean_data)
                order_line_items.append(line_item)
            except Exception as e:
                logger.warning(f"Failed to create line item: {e}")
                # Add to remarks instead of failing completely
                remarks = f"Failed to process line item: {e}"
                if 'material_code' in item_data and 'quantity' in item_data:
                    try:
                        line_item = OrderLineItem(
                            material_code=str(item_data['material_code']),
                            quantity=Decimal(str(item_data['quantity'])),
                            unit_of_measure='EA',
                            net_price=Decimal('0.00'),
                            currency='USD',
                            remarks=remarks
                        )
                        order_line_items.append(line_item)
                    except:
                        pass  # Skip this item entirely
        
        if not order_line_items:
            raise ValueError("No valid line items found")
        
        # Create order data
        order_data = {
            'order_number': order_info.get('order_number', f'UNKNOWN_{order_key}'),
            'customer_code': order_info.get('customer_code', 'UNKNOWN'),
            'delivery_date': order_info.get('delivery_date', datetime.now()),
            'line_items': order_line_items
        }
        
        # Add optional fields
        if 'order_date' in order_info:
            order_data['order_date'] = order_info['order_date']
        if 'customer_name' in order_info:
            order_data['customer_name'] = order_info['customer_name']
        
        return OrderData(**order_data)
